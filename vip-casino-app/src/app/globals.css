@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@300;400;500;600;700&display=swap');

:root {
  --background: #1a1a1a;
  --foreground: #f8f8f8;
  --gold: #b08d57;
  --gold-light: #d4940a;
  --royal-blue: #003366;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Montserrat', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: var(--gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gold-light);
}

@layer components {
  /* Luxury button styles */
  .btn-luxury {
    @apply bg-gradient-to-r from-yellow-600 to-yellow-500 text-white font-semibold py-3 px-8 rounded-lg shadow-lg transition-all duration-300 hover:from-yellow-500 hover:to-yellow-400 hover:shadow-xl transform hover:-translate-y-1;
  }

  .btn-outline-luxury {
    @apply border-2 border-yellow-500 text-yellow-500 font-semibold py-3 px-8 rounded-lg transition-all duration-300 hover:bg-yellow-500 hover:text-gray-900 transform hover:-translate-y-1;
  }

  /* Card styles */
  .card-luxury {
    @apply bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm;
  }

  /* Text gradient */
  .text-gradient {
    @apply bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent;
  }
}

/* Animations */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}
