import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>_Display, Mont<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>_<PERSON>mond } from "next/font/google";
import "./globals.css";

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const cormorantGaramond = Cormorant_Garamond({
  variable: "--font-cormorant",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "VIP Access Concierge - Exclusive Casino Experiences",
  description: "Your gateway to exclusive casino experiences. Discover world-class casinos with personalized VIP treatment and AI concierge service.",
  keywords: "VIP casino, luxury gaming, exclusive casino experiences, casino concierge, high-end gambling",
  authors: [{ name: "VIP Access Concierge" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#b08d57",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${playfairDisplay.variable} ${montserrat.variable} ${cormorantGaramond.variable} antialiased bg-gray-950 text-gray-100`}
      >
        {children}
      </body>
    </html>
  );
}
