'use client';

import { motion } from 'framer-motion';
import { ChevronRightIcon, ChatBubbleLeftRightIcon, SparklesIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

interface HomeScreenProps {
  onNavigate: (screen: string) => void;
}

export default function HomeScreen({ onNavigate }: HomeScreenProps) {
  const featuredCasinos = [
    {
      id: 1,
      name: "Paradise City",
      location: "Incheon, Korea",
      image: "https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=300&h=200&fit=crop&crop=center",
      description: "Korea's premier integrated resort",
      rating: 5,
    },
    {
      id: 2,
      name: "Walker Hill Casino",
      location: "Seoul, Korea",
      image: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300&h=200&fit=crop&crop=center",
      description: "Historic luxury casino with Han River views",
      rating: 5,
    },
    {
      id: 3,
      name: "Kangwon Land",
      location: "Gangwon, Korea",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop&crop=center",
      description: "Mountain resort casino experience",
      rating: 4,
    },
  ];

  const latestOffers = [
    {
      id: 1,
      title: "Seoul VIP Tournament",
      description: "Exclusive poker tournament at Walker Hill with ₩500M prize pool",
      date: "Dec 15-17, 2024",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=80&fit=crop&crop=center",
    },
    {
      id: 2,
      title: "Jeju Island Package",
      description: "3 nights luxury suite at Lotte Hotel Jeju with gaming credits",
      date: "Available Now",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=80&fit=crop&crop=center",
    },
    {
      id: 3,
      title: "Paradise City Experience",
      description: "Complimentary helicopter transfer to Incheon Paradise City",
      date: "Limited Time",
      image: "https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=100&h=80&fit=crop&crop=center",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <motion.header
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="relative px-6 py-4 bg-gray-900/50 backdrop-blur-md border-b border-gray-700"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-700 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">V</span>
            </div>
            <div>
              <h1 className="text-lg font-serif font-semibold text-white">VIP Access</h1>
              <p className="text-xs text-yellow-400">Concierge</p>
            </div>
          </div>
          <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="relative px-6 py-8"
      >
        <div className="relative h-48 rounded-2xl overflow-hidden bg-gradient-to-r from-yellow-900/20 to-blue-900/20 border border-yellow-500/20">
          <img
            src="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800&h=400&fit=crop&crop=center"
            alt="Seoul skyline"
            className="absolute inset-0 w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent"></div>
          <div className="relative z-10 h-full flex flex-col justify-center px-6">
            <motion.div
              initial={{ x: -30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h2 className="text-2xl font-serif font-bold text-white mb-2">
                Korea's Premier Gaming
              </h2>
              <p className="text-yellow-300 mb-4 text-sm">
                Experience luxury gaming at Korea's most exclusive casino destinations
              </p>
              <button
                onClick={() => onNavigate('casinos')}
                className="bg-gradient-to-r from-yellow-600 to-yellow-500 text-white font-semibold py-3 px-8 rounded-lg shadow-lg transition-all duration-300 hover:from-yellow-500 hover:to-yellow-400 hover:shadow-xl transform hover:-translate-y-1 text-sm"
              >
                Explore Destinations
                <ChevronRightIcon className="w-4 h-4 ml-2 inline" />
              </button>
            </motion.div>
          </div>
          <div className="absolute top-4 right-4">
            <SparklesIcon className="w-8 h-8 text-yellow-400 animate-pulse" />
          </div>
        </div>
      </motion.section>

      {/* Featured Destinations */}
      <motion.section
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="px-6 py-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-serif font-semibold text-white">Featured Destinations</h3>
          <button
            onClick={() => onNavigate('casinos')}
            className="text-yellow-400 text-sm font-medium"
          >
            View All
          </button>
        </div>

        <div className="flex space-x-4 overflow-x-auto pb-4">
          {featuredCasinos.map((casino, index) => (
            <motion.div
              key={casino.id}
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              onClick={() => onNavigate('casino-detail')}
              className="flex-shrink-0 w-64 bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 cursor-pointer hover:shadow-xl transition-all duration-300"
            >
              <div className="h-32 bg-gray-800 rounded-lg mb-3 relative overflow-hidden">
                <img
                  src={casino.image}
                  alt={casino.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-2 left-2">
                  <div className="flex">
                    {[...Array(casino.rating)].map((_, i) => (
                      <SparklesIcon key={i} className="w-3 h-3 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </div>
              <h4 className="font-semibold text-white text-sm mb-1">{casino.name}</h4>
              <p className="text-yellow-400 text-xs mb-2">{casino.location}</p>
              <p className="text-gray-400 text-xs">{casino.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Latest Offers */}
      <motion.section
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="px-6 py-6"
      >
        <h3 className="text-xl font-serif font-semibold text-white mb-4">Latest Offers & Events</h3>

        <div className="space-y-3">
          {latestOffers.map((offer, index) => (
            <motion.div
              key={offer.id}
              initial={{ x: -30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
              className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 flex items-center space-x-4 cursor-pointer hover:shadow-xl transition-all duration-300"
            >
              <div className="w-16 h-12 bg-gray-800 rounded-lg flex-shrink-0 overflow-hidden">
                <img
                  src={offer.image}
                  alt={offer.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-white text-sm mb-1">{offer.title}</h4>
                <p className="text-gray-400 text-xs mb-1">{offer.description}</p>
                <p className="text-yellow-400 text-xs">{offer.date}</p>
              </div>
              <ChevronRightIcon className="w-5 h-5 text-yellow-400" />
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* AI Concierge FAB */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 1 }}
        onClick={() => onNavigate('chat')}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-full shadow-2xl flex items-center justify-center z-40"
      >
        <ChatBubbleLeftRightIcon className="w-6 h-6 text-white" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs">1</span>
        </div>
      </motion.button>
    </div>
  );
}
