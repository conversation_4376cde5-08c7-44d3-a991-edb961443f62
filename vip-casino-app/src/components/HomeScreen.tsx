'use client';

import { motion } from 'framer-motion';
import { ChevronRightIcon, ChatBubbleLeftRightIcon, SparklesIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

interface HomeScreenProps {
  onNavigate: (screen: string) => void;
}

export default function HomeScreen({ onNavigate }: HomeScreenProps) {
  const featuredCasinos = [
    {
      id: 1,
      name: "Marina Bay Sands",
      location: "Singapore",
      image: "/api/placeholder/300/200",
      description: "Iconic luxury resort with world-class gaming",
      rating: 5,
    },
    {
      id: 2,
      name: "The Venetian Macao",
      location: "Macau",
      image: "/api/placeholder/300/200",
      description: "Asia's largest casino resort",
      rating: 5,
    },
    {
      id: 3,
      name: "Crown Melbourne",
      location: "Australia",
      image: "/api/placeholder/300/200",
      description: "Premium entertainment destination",
      rating: 4,
    },
  ];

  const latestOffers = [
    {
      id: 1,
      title: "Exclusive VIP Tournament",
      description: "Join our private poker tournament with $1M prize pool",
      date: "Dec 15-17, 2024",
      image: "/api/placeholder/100/80",
    },
    {
      id: 2,
      title: "Luxury Suite Package",
      description: "3 nights in presidential suite with gaming credits",
      date: "Available Now",
      image: "/api/placeholder/100/80",
    },
    {
      id: 3,
      title: "Private Jet Experience",
      description: "Complimentary private jet to Macau casinos",
      date: "Limited Time",
      image: "/api/placeholder/100/80",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <motion.header
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="relative px-6 py-4 bg-gray-900/50 backdrop-blur-md border-b border-gray-700"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-700 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">V</span>
            </div>
            <div>
              <h1 className="text-lg font-serif font-semibold text-white">VIP Access</h1>
              <p className="text-xs text-yellow-400">Concierge</p>
            </div>
          </div>
          <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="relative px-6 py-8"
      >
        <div className="relative h-48 rounded-2xl overflow-hidden bg-gradient-to-r from-gold-900/20 to-royal-900/20 border border-gold-500/20">
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent"></div>
          <div className="relative z-10 h-full flex flex-col justify-center px-6">
            <motion.div
              initial={{ x: -30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h2 className="text-2xl font-serif font-bold text-white mb-2">
                Exclusive Access Awaits
              </h2>
              <p className="text-gold-300 mb-4 text-sm">
                Discover world-class casinos with personalized VIP treatment
              </p>
              <button
                onClick={() => onNavigate('casinos')}
                className="btn-luxury text-sm"
              >
                Explore Destinations
                <ChevronRightIcon className="w-4 h-4 ml-2 inline" />
              </button>
            </motion.div>
          </div>
          <div className="absolute top-4 right-4">
            <SparklesIcon className="w-8 h-8 text-gold-400 animate-pulse" />
          </div>
        </div>
      </motion.section>

      {/* Featured Destinations */}
      <motion.section
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="px-6 py-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-serif font-semibold text-white">Featured Destinations</h3>
          <button
            onClick={() => onNavigate('casinos')}
            className="text-gold-400 text-sm font-medium"
          >
            View All
          </button>
        </div>

        <div className="flex space-x-4 overflow-x-auto pb-4">
          {featuredCasinos.map((casino, index) => (
            <motion.div
              key={casino.id}
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              onClick={() => onNavigate('casino-detail')}
              className="flex-shrink-0 w-64 card-luxury p-4 cursor-pointer hover:shadow-gold transition-all duration-300"
            >
              <div className="h-32 bg-dark-800 rounded-lg mb-3 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-2 left-2">
                  <div className="flex">
                    {[...Array(casino.rating)].map((_, i) => (
                      <SparklesIcon key={i} className="w-3 h-3 text-gold-400" />
                    ))}
                  </div>
                </div>
              </div>
              <h4 className="font-semibold text-white text-sm mb-1">{casino.name}</h4>
              <p className="text-gold-400 text-xs mb-2">{casino.location}</p>
              <p className="text-gray-400 text-xs">{casino.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Latest Offers */}
      <motion.section
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="px-6 py-6"
      >
        <h3 className="text-xl font-serif font-semibold text-white mb-4">Latest Offers & Events</h3>

        <div className="space-y-3">
          {latestOffers.map((offer, index) => (
            <motion.div
              key={offer.id}
              initial={{ x: -30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
              className="card-luxury p-4 flex items-center space-x-4 cursor-pointer hover:shadow-gold transition-all duration-300"
            >
              <div className="w-16 h-12 bg-dark-800 rounded-lg flex-shrink-0"></div>
              <div className="flex-1">
                <h4 className="font-semibold text-white text-sm mb-1">{offer.title}</h4>
                <p className="text-gray-400 text-xs mb-1">{offer.description}</p>
                <p className="text-gold-400 text-xs">{offer.date}</p>
              </div>
              <ChevronRightIcon className="w-5 h-5 text-gold-400" />
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* AI Concierge FAB */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 1 }}
        onClick={() => onNavigate('chat')}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-gold-600 to-gold-500 rounded-full shadow-luxury flex items-center justify-center z-40"
      >
        <ChatBubbleLeftRightIcon className="w-6 h-6 text-white" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs">1</span>
        </div>
      </motion.button>
    </div>
  );
}
