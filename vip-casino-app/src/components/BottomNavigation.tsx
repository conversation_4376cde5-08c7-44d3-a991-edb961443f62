'use client';

import { motion } from 'framer-motion';
import {
  HomeIcon,
  BuildingOffice2Icon,
  ChatBubbleLeftRightIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  BuildingOffice2Icon as BuildingOffice2IconSolid,
  ChatBubbleLeftRightIcon as ChatBubbleLeftRightIconSolid,
  UserIcon as UserIconSolid
} from '@heroicons/react/24/solid';

interface BottomNavigationProps {
  activeScreen: string;
  onNavigate: (screen: string) => void;
}

export default function BottomNavigation({ activeScreen, onNavigate }: BottomNavigationProps) {
  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
    },
    {
      id: 'casinos',
      label: 'Casinos',
      icon: BuildingOffice2Icon,
      iconSolid: BuildingOffice2IconSolid,
    },
    {
      id: 'chat',
      label: 'Chat',
      icon: ChatBubbleLeftRightIcon,
      iconSolid: ChatBubbleLeftRightIconSolid,
      badge: true,
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: UserIcon,
      iconSolid: UserIconSolid,
    },
  ];

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed bottom-0 left-0 right-0 z-50"
    >
      <div className="bg-gray-900/95 backdrop-blur-md border-t border-gray-700 px-4 py-2">
        <div className="flex items-center justify-around">
          {navItems.map((item) => {
            const isActive = activeScreen === item.id;
            const Icon = isActive ? item.iconSolid : item.icon;

            return (
              <motion.button
                key={item.id}
                onClick={() => onNavigate(item.id)}
                className="relative flex flex-col items-center py-2 px-3 min-w-[60px]"
                whileTap={{ scale: 0.95 }}
              >
                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-yellow-500 rounded-full"
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}

                {/* Icon container */}
                <div className="relative">
                  <Icon
                    className={`w-6 h-6 transition-colors duration-200 ${
                      isActive ? 'text-yellow-400' : 'text-gray-400'
                    }`}
                  />

                  {/* Badge */}
                  {item.badge && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center"
                    >
                      <span className="text-white text-xs">1</span>
                    </motion.div>
                  )}
                </div>

                {/* Label */}
                <span
                  className={`text-xs mt-1 transition-colors duration-200 ${
                    isActive ? 'text-yellow-400 font-medium' : 'text-gray-400'
                  }`}
                >
                  {item.label}
                </span>

                {/* Ripple effect */}
                {isActive && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0.5 }}
                    animate={{ scale: 1.5, opacity: 0 }}
                    transition={{ duration: 0.6 }}
                    className="absolute inset-0 bg-yellow-400/20 rounded-full"
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
}
