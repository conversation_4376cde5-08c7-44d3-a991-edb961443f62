'use client';

import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  MapPinIcon,
  SparklesIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { useState } from 'react';

interface CasinoListScreenProps {
  onNavigate: (screen: string) => void;
  onBack: () => void;
}

export default function CasinoListScreen({ onNavigate, onBack }: CasinoListScreenProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const casinos = [
    {
      id: 1,
      name: "Marina Bay Sands",
      location: "Singapore",
      country: "Singapore",
      image: "/api/placeholder/400/250",
      description: "Iconic luxury resort with world-class gaming and entertainment",
      rating: 5,
      vipLevel: "Platinum",
      features: ["Private Gaming Rooms", "Michelin Restaurants", "Infinity Pool"],
      minBet: "$100",
    },
    {
      id: 2,
      name: "The Venetian Macao",
      location: "Cotai Strip, Macau",
      country: "Macau",
      image: "/api/placeholder/400/250",
      description: "Asia's largest casino resort with Venetian-themed luxury",
      rating: 5,
      vipLevel: "Diamond",
      features: ["Largest Gaming Floor", "Shopping Mall", "Gondola Rides"],
      minBet: "$50",
    },
    {
      id: 3,
      name: "Crown Melbourne",
      location: "Melbourne",
      country: "Australia",
      image: "/api/placeholder/400/250",
      description: "Premium entertainment destination on the Yarra River",
      rating: 4,
      vipLevel: "Gold",
      features: ["River Views", "Fine Dining", "Theater Shows"],
      minBet: "$25",
    },
    {
      id: 4,
      name: "City of Dreams",
      location: "Cotai, Macau",
      country: "Macau",
      image: "/api/placeholder/400/250",
      description: "Integrated resort with spectacular entertainment",
      rating: 5,
      vipLevel: "Platinum",
      features: ["House of Dancing Water", "Luxury Suites", "High Limit Rooms"],
      minBet: "$100",
    },
    {
      id: 5,
      name: "Resorts World Sentosa",
      location: "Sentosa Island",
      country: "Singapore",
      image: "/api/placeholder/400/250",
      description: "Integrated resort with Universal Studios and casino",
      rating: 4,
      vipLevel: "Gold",
      features: ["Universal Studios", "S.E.A. Aquarium", "Beach Access"],
      minBet: "$50",
    },
    {
      id: 6,
      name: "Wynn Palace",
      location: "Cotai, Macau",
      country: "Macau",
      image: "/api/placeholder/400/250",
      description: "Luxury resort with stunning floral displays and fountains",
      rating: 5,
      vipLevel: "Diamond",
      features: ["SkyCabs", "Performance Lake", "Luxury Shopping"],
      minBet: "$200",
    },
  ];

  const getVipLevelColor = (level: string) => {
    switch (level) {
      case 'Diamond': return 'text-blue-400 bg-blue-400/10';
      case 'Platinum': return 'text-gray-300 bg-gray-300/10';
      case 'Gold': return 'text-yellow-400 bg-yellow-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const filteredCasinos = casinos.filter(casino =>
    casino.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    casino.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
    casino.country.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-950 pb-20">
      {/* Header */}
      <motion.header
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="sticky top-0 z-40 px-6 py-4 bg-gray-900/95 backdrop-blur-md border-b border-gray-700"
      >
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center"
          >
            <ChevronRightIcon className="w-5 h-5 text-yellow-400 rotate-180" />
          </button>
          <h1 className="text-xl font-serif font-semibold text-white">Destinations</h1>
          <button className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center">
            <AdjustmentsHorizontalIcon className="w-5 h-5 text-yellow-400" />
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search casinos, locations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500 transition-colors"
          />
        </div>
      </motion.header>

      {/* Casino List */}
      <div className="px-6 py-6">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-4"
        >
          <p className="text-gray-400 text-sm">
            {filteredCasinos.length} premium destinations available
          </p>
        </motion.div>

        <div className="space-y-4">
          {filteredCasinos.map((casino, index) => (
            <motion.div
              key={casino.id}
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              onClick={() => onNavigate('casino-detail')}
              className="bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 cursor-pointer hover:shadow-xl transition-all duration-300"
            >
              {/* Casino Image */}
              <div className="h-48 bg-gray-800 rounded-xl mb-4 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                {/* VIP Level Badge */}
                <div className="absolute top-3 left-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getVipLevelColor(casino.vipLevel)}`}>
                    {casino.vipLevel} VIP
                  </span>
                </div>

                {/* Rating */}
                <div className="absolute top-3 right-3 flex items-center space-x-1 bg-black/50 rounded-full px-2 py-1">
                  {[...Array(casino.rating)].map((_, i) => (
                    <SparklesIcon key={i} className="w-3 h-3 text-yellow-400" />
                  ))}
                </div>

                {/* Min Bet */}
                <div className="absolute bottom-3 right-3 bg-yellow-500/20 backdrop-blur-sm rounded-lg px-3 py-1">
                  <span className="text-yellow-400 text-xs font-medium">From {casino.minBet}</span>
                </div>
              </div>

              {/* Casino Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-serif font-semibold text-white mb-1">
                    {casino.name}
                  </h3>
                  <div className="flex items-center text-yellow-400 text-sm">
                    <MapPinIcon className="w-4 h-4 mr-1" />
                    {casino.location}, {casino.country}
                  </div>
                </div>

                <p className="text-gray-400 text-sm leading-relaxed">
                  {casino.description}
                </p>

                {/* Features */}
                <div className="flex flex-wrap gap-2">
                  {casino.features.slice(0, 3).map((feature, i) => (
                    <span
                      key={i}
                      className="px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded-md"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Action Button */}
                <div className="flex items-center justify-between pt-2">
                  <span className="text-gray-400 text-sm">Tap to explore</span>
                  <ChevronRightIcon className="w-5 h-5 text-yellow-400" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
