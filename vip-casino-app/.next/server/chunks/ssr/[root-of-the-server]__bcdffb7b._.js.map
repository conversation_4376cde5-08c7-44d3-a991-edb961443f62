{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/SplashScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\n\ninterface SplashScreenProps {\n  onComplete: () => void;\n}\n\nexport default function SplashScreen({ onComplete }: SplashScreenProps) {\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(onComplete, 500); // Wait for fade out animation\n    }, 3000);\n\n    return () => clearTimeout(timer);\n  }, [onComplete]);\n\n  return (\n    <motion.div\n      initial={{ opacity: 1 }}\n      animate={{ opacity: isVisible ? 1 : 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-yellow-500/20 to-transparent transform -skew-y-12\"></div>\n        <div className=\"absolute inset-0 bg-gradient-to-l from-transparent via-blue-950/30 to-transparent transform skew-y-12\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 text-center\">\n        {/* Logo */}\n        <motion.div\n          initial={{ scale: 0.5, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <div className=\"relative\">\n            {/* Logo Background Circle */}\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute inset-0 w-32 h-32 mx-auto rounded-full border-2 border-yellow-500/30\"\n            ></motion.div>\n\n            {/* Logo Text */}\n            <div className=\"relative w-32 h-32 mx-auto flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-elegant font-bold text-gradient\">\n                  VIP\n                </div>\n                <div className=\"text-sm font-sans tracking-widest text-yellow-400\">\n                  ACCESS\n                </div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Tagline */}\n        <motion.div\n          initial={{ y: 20, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"space-y-2\"\n        >\n          <h1 className=\"text-xl font-serif text-yellow-300\">\n            Your Gateway to Exclusive\n          </h1>\n          <h2 className=\"text-2xl font-serif font-semibold text-gradient\">\n            Casino Experiences\n          </h2>\n        </motion.div>\n\n        {/* Loading Animation */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 1.5 }}\n          className=\"mt-12\"\n        >\n          <div className=\"flex justify-center space-x-2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.5, 1, 0.5],\n                }}\n                transition={{\n                  duration: 1.5,\n                  repeat: Infinity,\n                  delay: i * 0.2,\n                }}\n                className=\"w-2 h-2 bg-yellow-500 rounded-full\"\n              ></motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Decorative Elements */}\n      <motion.div\n        animate={{ rotate: 360 }}\n        transition={{ duration: 30, repeat: Infinity, ease: \"linear\" }}\n        className=\"absolute top-20 left-20 w-4 h-4 border border-yellow-500/30 rotate-45\"\n      ></motion.div>\n\n      <motion.div\n        animate={{ rotate: -360 }}\n        transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n        className=\"absolute bottom-20 right-20 w-6 h-6 border border-blue-500/30 rounded-full\"\n      ></motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,aAAa,EAAE,UAAU,EAAqB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;YACb,WAAW,YAAY,MAAM,8BAA8B;QAC7D,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS,YAAY,IAAI;QAAE;QACtC,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;0DAG/D,8OAAC;gDAAI,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;;;;;;;kCAMlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;oCACA,WAAU;mCAVL;;;;;;;;;;;;;;;;;;;;;0BAkBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;gBAC7D,WAAU;;;;;;0BAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,QAAQ,CAAC;gBAAI;gBACxB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;gBAC7D,WAAU;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/HomeScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ChevronRightIcon, ChatBubbleLeftRightIcon, SparklesIcon } from '@heroicons/react/24/outline';\nimport Image from 'next/image';\n\ninterface HomeScreenProps {\n  onNavigate: (screen: string) => void;\n}\n\nexport default function HomeScreen({ onNavigate }: HomeScreenProps) {\n  const featuredCasinos = [\n    {\n      id: 1,\n      name: \"Paradise City\",\n      location: \"Incheon, Korea\",\n      image: \"https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=300&h=200&fit=crop&crop=center\",\n      description: \"Korea's premier integrated resort\",\n      rating: 5,\n    },\n    {\n      id: 2,\n      name: \"Walker Hill Casino\",\n      location: \"Seoul, Korea\",\n      image: \"https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=300&h=200&fit=crop&crop=center\",\n      description: \"Historic luxury casino with Han River views\",\n      rating: 5,\n    },\n    {\n      id: 3,\n      name: \"Kangwon Land\",\n      location: \"Gangwon, Korea\",\n      image: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop&crop=center\",\n      description: \"Mountain resort casino experience\",\n      rating: 4,\n    },\n  ];\n\n  const latestOffers = [\n    {\n      id: 1,\n      title: \"Seoul VIP Tournament\",\n      description: \"Exclusive poker tournament at Walker Hill with ₩500M prize pool\",\n      date: \"Dec 15-17, 2024\",\n      image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=80&fit=crop&crop=center\",\n    },\n    {\n      id: 2,\n      title: \"Jeju Island Package\",\n      description: \"3 nights luxury suite at Lotte Hotel Jeju with gaming credits\",\n      date: \"Available Now\",\n      image: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=80&fit=crop&crop=center\",\n    },\n    {\n      id: 3,\n      title: \"Paradise City Experience\",\n      description: \"Complimentary helicopter transfer to Incheon Paradise City\",\n      date: \"Limited Time\",\n      image: \"https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=100&h=80&fit=crop&crop=center\",\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-950\">\n      {/* Header */}\n      <motion.header\n        initial={{ y: -50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className=\"relative px-6 py-4 bg-gray-900/50 backdrop-blur-md border-b border-gray-700\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">V</span>\n            </div>\n            <div>\n              <h1 className=\"text-lg font-serif font-semibold text-white\">VIP Access</h1>\n              <p className=\"text-xs text-yellow-400\">Concierge</p>\n            </div>\n          </div>\n          <div className=\"w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center\">\n            <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"></div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Hero Section */}\n      <motion.section\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.8, delay: 0.2 }}\n        className=\"relative px-6 py-8\"\n      >\n        <div className=\"relative h-48 rounded-2xl overflow-hidden bg-gradient-to-r from-yellow-900/20 to-blue-900/20 border border-yellow-500/20\">\n          <img\n            src=\"https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800&h=400&fit=crop&crop=center\"\n            alt=\"Seoul skyline\"\n            className=\"absolute inset-0 w-full h-full object-cover opacity-30\"\n          />\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black/60 to-transparent\"></div>\n          <div className=\"relative z-10 h-full flex flex-col justify-center px-6\">\n            <motion.div\n              initial={{ x: -30, opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n            >\n              <h2 className=\"text-2xl font-serif font-bold text-white mb-2\">\n                Korea's Premier Gaming\n              </h2>\n              <p className=\"text-yellow-300 mb-4 text-sm\">\n                Experience luxury gaming at Korea's most exclusive casino destinations\n              </p>\n              <button\n                onClick={() => onNavigate('casinos')}\n                className=\"bg-gradient-to-r from-yellow-600 to-yellow-500 text-white font-semibold py-3 px-8 rounded-lg shadow-lg transition-all duration-300 hover:from-yellow-500 hover:to-yellow-400 hover:shadow-xl transform hover:-translate-y-1 text-sm\"\n              >\n                Explore Destinations\n                <ChevronRightIcon className=\"w-4 h-4 ml-2 inline\" />\n              </button>\n            </motion.div>\n          </div>\n          <div className=\"absolute top-4 right-4\">\n            <SparklesIcon className=\"w-8 h-8 text-yellow-400 animate-pulse\" />\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Destinations */}\n      <motion.section\n        initial={{ y: 30, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6, delay: 0.4 }}\n        className=\"px-6 py-6\"\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-serif font-semibold text-white\">Featured Destinations</h3>\n          <button\n            onClick={() => onNavigate('casinos')}\n            className=\"text-yellow-400 text-sm font-medium\"\n          >\n            View All\n          </button>\n        </div>\n\n        <div className=\"flex space-x-4 overflow-x-auto pb-4\">\n          {featuredCasinos.map((casino, index) => (\n            <motion.div\n              key={casino.id}\n              initial={{ x: 50, opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n              onClick={() => onNavigate('casino-detail')}\n              className=\"flex-shrink-0 w-64 bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 cursor-pointer hover:shadow-xl transition-all duration-300\"\n            >\n              <div className=\"h-32 bg-gray-800 rounded-lg mb-3 relative overflow-hidden\">\n                <img\n                  src={casino.image}\n                  alt={casino.name}\n                  className=\"w-full h-full object-cover\"\n                  onError={(e) => {\n                    e.currentTarget.style.display = 'none';\n                  }}\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                <div className=\"absolute bottom-2 left-2\">\n                  <div className=\"flex\">\n                    {[...Array(casino.rating)].map((_, i) => (\n                      <SparklesIcon key={i} className=\"w-3 h-3 text-yellow-400\" />\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <h4 className=\"font-semibold text-white text-sm mb-1\">{casino.name}</h4>\n              <p className=\"text-yellow-400 text-xs mb-2\">{casino.location}</p>\n              <p className=\"text-gray-400 text-xs\">{casino.description}</p>\n            </motion.div>\n          ))}\n        </div>\n      </motion.section>\n\n      {/* Latest Offers */}\n      <motion.section\n        initial={{ y: 30, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6, delay: 0.6 }}\n        className=\"px-6 py-6\"\n      >\n        <h3 className=\"text-xl font-serif font-semibold text-white mb-4\">Latest Offers & Events</h3>\n\n        <div className=\"space-y-3\">\n          {latestOffers.map((offer, index) => (\n            <motion.div\n              key={offer.id}\n              initial={{ x: -30, opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}\n              className=\"bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 flex items-center space-x-4 cursor-pointer hover:shadow-xl transition-all duration-300\"\n            >\n              <div className=\"w-16 h-12 bg-gray-800 rounded-lg flex-shrink-0 overflow-hidden\">\n                <img\n                  src={offer.image}\n                  alt={offer.title}\n                  className=\"w-full h-full object-cover\"\n                  onError={(e) => {\n                    e.currentTarget.style.display = 'none';\n                  }}\n                />\n              </div>\n              <div className=\"flex-1\">\n                <h4 className=\"font-semibold text-white text-sm mb-1\">{offer.title}</h4>\n                <p className=\"text-gray-400 text-xs mb-1\">{offer.description}</p>\n                <p className=\"text-yellow-400 text-xs\">{offer.date}</p>\n              </div>\n              <ChevronRightIcon className=\"w-5 h-5 text-yellow-400\" />\n            </motion.div>\n          ))}\n        </div>\n      </motion.section>\n\n      {/* AI Concierge FAB */}\n      <motion.button\n        initial={{ scale: 0 }}\n        animate={{ scale: 1 }}\n        transition={{ duration: 0.5, delay: 1 }}\n        onClick={() => onNavigate('chat')}\n        className=\"fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-yellow-600 to-yellow-500 rounded-full shadow-2xl flex items-center justify-center z-40\"\n      >\n        <ChatBubbleLeftRightIcon className=\"w-6 h-6 text-white\" />\n        <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center\">\n          <span className=\"text-white text-xs\">1</span>\n        </div>\n      </motion.button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAUe,SAAS,WAAW,EAAE,UAAU,EAAmB;IAChE,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;KACD;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAG5C,8OAAC;wCACC,SAAS,IAAM,WAAW;wCAC1B,WAAU;;4CACX;0DAEC,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCACC,SAAS,IAAM,WAAW;gCAC1B,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,SAAS,IAAM,WAAW;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,OAAO,KAAK;gDACjB,KAAK,OAAO,IAAI;gDAChB,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;;0DAEF,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,OAAO,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,uNAAA,CAAA,eAAY;4DAAS,WAAU;2DAAb;;;;;;;;;;;;;;;;;;;;;kDAK3B,8OAAC;wCAAG,WAAU;kDAAyC,OAAO,IAAI;;;;;;kDAClE,8OAAC;wCAAE,WAAU;kDAAgC,OAAO,QAAQ;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BA3BnD,OAAO,EAAE;;;;;;;;;;;;;;;;0BAkCtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAEjE,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,MAAM,KAAK;4CAChB,KAAK,MAAM,KAAK;4CAChB,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;4CAClC;;;;;;;;;;;kDAGJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC,MAAM,KAAK;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAA8B,MAAM,WAAW;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAA2B,MAAM,IAAI;;;;;;;;;;;;kDAEpD,8OAAC,+NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;;+BArBvB,MAAM,EAAE;;;;;;;;;;;;;;;;0BA4BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAE;gBACtC,SAAS,IAAM,WAAW;gBAC1B,WAAU;;kCAEV,8OAAC,6OAAA,CAAA,0BAAuB;wBAAC,WAAU;;;;;;kCACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/CasinoListScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport {\n  MagnifyingGlassIcon,\n  AdjustmentsHorizontalIcon,\n  MapPinIcon,\n  SparklesIcon,\n  ChevronRightIcon\n} from '@heroicons/react/24/outline';\nimport { useState } from 'react';\n\ninterface CasinoListScreenProps {\n  onNavigate: (screen: string) => void;\n  onBack: () => void;\n}\n\nexport default function CasinoListScreen({ onNavigate, onBack }: CasinoListScreenProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const casinos = [\n    {\n      id: 1,\n      name: \"Paradise City\",\n      location: \"Incheon\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=400&h=250&fit=crop&crop=center\",\n      description: \"Korea's premier integrated resort with luxury gaming and entertainment\",\n      rating: 5,\n      vipLevel: \"Platinum\",\n      features: [\"Private Gaming Rooms\", \"Michelin Restaurants\", \"Art Paradise\"],\n      minBet: \"₩100,000\",\n    },\n    {\n      id: 2,\n      name: \"Grand Korea Leisure Seoul\",\n      location: \"Gangnam, Seoul\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400&h=250&fit=crop&crop=center\",\n      description: \"Sophisticated gaming destination in the heart of Seoul\",\n      rating: 5,\n      vipLevel: \"Diamond\",\n      features: [\"VIP Lounges\", \"Fine Dining\", \"City Views\"],\n      minBet: \"₩50,000\",\n    },\n    {\n      id: 3,\n      name: \"Kangwon Land\",\n      location: \"Jeongseon, Gangwon\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop&crop=center\",\n      description: \"Mountain resort casino with stunning natural surroundings\",\n      rating: 4,\n      vipLevel: \"Gold\",\n      features: [\"Mountain Views\", \"Ski Resort\", \"Golf Course\"],\n      minBet: \"₩25,000\",\n    },\n    {\n      id: 4,\n      name: \"Seven Luck Casino Seoul\",\n      location: \"Gangnam, Seoul\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&crop=center\",\n      description: \"Premium casino experience in Seoul's business district\",\n      rating: 5,\n      vipLevel: \"Platinum\",\n      features: [\"Business Center\", \"Luxury Suites\", \"Shopping Mall\"],\n      minBet: \"₩100,000\",\n    },\n    {\n      id: 5,\n      name: \"Lotte Hotel Jeju Casino\",\n      location: \"Jeju Island\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop&crop=center\",\n      description: \"Island paradise casino with ocean views and luxury amenities\",\n      rating: 4,\n      vipLevel: \"Gold\",\n      features: [\"Ocean Views\", \"Spa Resort\", \"Beach Access\"],\n      minBet: \"₩50,000\",\n    },\n    {\n      id: 6,\n      name: \"Walker Hill Casino\",\n      location: \"Gwangjin, Seoul\",\n      country: \"South Korea\",\n      image: \"https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=250&fit=crop&crop=center\",\n      description: \"Historic luxury casino with Han River views and entertainment\",\n      rating: 5,\n      vipLevel: \"Diamond\",\n      features: [\"River Views\", \"Theater Shows\", \"Luxury Shopping\"],\n      minBet: \"₩200,000\",\n    },\n  ];\n\n  const getVipLevelColor = (level: string) => {\n    switch (level) {\n      case 'Diamond': return 'text-blue-400 bg-blue-400/10';\n      case 'Platinum': return 'text-gray-300 bg-gray-300/10';\n      case 'Gold': return 'text-yellow-400 bg-yellow-400/10';\n      default: return 'text-gray-400 bg-gray-400/10';\n    }\n  };\n\n  const filteredCasinos = casinos.filter(casino =>\n    casino.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    casino.location.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    casino.country.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-950 pb-20\">\n      {/* Header */}\n      <motion.header\n        initial={{ y: -50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className=\"sticky top-0 z-40 px-6 py-4 bg-gray-900/95 backdrop-blur-md border-b border-gray-700\"\n      >\n        <div className=\"flex items-center justify-between mb-4\">\n          <button\n            onClick={onBack}\n            className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center\"\n          >\n            <ChevronRightIcon className=\"w-5 h-5 text-yellow-400 rotate-180\" />\n          </button>\n          <h1 className=\"text-xl font-serif font-semibold text-white\">Destinations</h1>\n          <button className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center\">\n            <AdjustmentsHorizontalIcon className=\"w-5 h-5 text-yellow-400\" />\n          </button>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"relative\">\n          <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search casinos, locations...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500 transition-colors\"\n          />\n        </div>\n      </motion.header>\n\n      {/* Casino List */}\n      <div className=\"px-6 py-6\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"mb-4\"\n        >\n          <p className=\"text-gray-400 text-sm\">\n            {filteredCasinos.length} premium destinations available\n          </p>\n        </motion.div>\n\n        <div className=\"space-y-4\">\n          {filteredCasinos.map((casino, index) => (\n            <motion.div\n              key={casino.id}\n              initial={{ y: 30, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              onClick={() => onNavigate('casino-detail')}\n              className=\"bg-gray-900 border border-gray-700 rounded-xl shadow-2xl backdrop-blur-sm p-4 cursor-pointer hover:shadow-xl transition-all duration-300\"\n            >\n              {/* Casino Image */}\n              <div className=\"h-48 bg-gray-800 rounded-xl mb-4 relative overflow-hidden\">\n                <img\n                  src={casino.image}\n                  alt={casino.name}\n                  className=\"w-full h-full object-cover\"\n                  onError={(e) => {\n                    e.currentTarget.style.display = 'none';\n                  }}\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"></div>\n\n                {/* VIP Level Badge */}\n                <div className=\"absolute top-3 left-3\">\n                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getVipLevelColor(casino.vipLevel)}`}>\n                    {casino.vipLevel} VIP\n                  </span>\n                </div>\n\n                {/* Rating */}\n                <div className=\"absolute top-3 right-3 flex items-center space-x-1 bg-black/50 rounded-full px-2 py-1\">\n                  {[...Array(casino.rating)].map((_, i) => (\n                    <SparklesIcon key={i} className=\"w-3 h-3 text-yellow-400\" />\n                  ))}\n                </div>\n\n                {/* Min Bet */}\n                <div className=\"absolute bottom-3 right-3 bg-yellow-500/20 backdrop-blur-sm rounded-lg px-3 py-1\">\n                  <span className=\"text-yellow-400 text-xs font-medium\">From {casino.minBet}</span>\n                </div>\n              </div>\n\n              {/* Casino Info */}\n              <div className=\"space-y-3\">\n                <div>\n                  <h3 className=\"text-lg font-serif font-semibold text-white mb-1\">\n                    {casino.name}\n                  </h3>\n                  <div className=\"flex items-center text-yellow-400 text-sm\">\n                    <MapPinIcon className=\"w-4 h-4 mr-1\" />\n                    {casino.location}, {casino.country}\n                  </div>\n                </div>\n\n                <p className=\"text-gray-400 text-sm leading-relaxed\">\n                  {casino.description}\n                </p>\n\n                {/* Features */}\n                <div className=\"flex flex-wrap gap-2\">\n                  {casino.features.slice(0, 3).map((feature, i) => (\n                    <span\n                      key={i}\n                      className=\"px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded-md\"\n                    >\n                      {feature}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Action Button */}\n                <div className=\"flex items-center justify-between pt-2\">\n                  <span className=\"text-gray-400 text-sm\">Tap to explore</span>\n                  <ChevronRightIcon className=\"w-5 h-5 text-yellow-400\" />\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAVA;;;;;AAiBe,SAAS,iBAAiB,EAAE,UAAU,EAAE,MAAM,EAAyB;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAwB;gBAAwB;aAAe;YAC1E,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAe;gBAAe;aAAa;YACtD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAkB;gBAAc;aAAc;YACzD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAmB;gBAAiB;aAAgB;YAC/D,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAe;gBAAc;aAAe;YACvD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,UAAU;gBAAC;gBAAe;gBAAiB;aAAkB;YAC7D,QAAQ;QACV;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC9D,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;0CAE9B,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;;gCACV,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAI5B,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,SAAS,IAAM,WAAW;gCAC1B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,OAAO,KAAK;gDACjB,KAAK,OAAO,IAAI;gDAChB,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;;0DAEF,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,OAAO,QAAQ,GAAG;;wDAC/F,OAAO,QAAQ;wDAAC;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,OAAO,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,uNAAA,CAAA,eAAY;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAKvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAsC;wDAAM,OAAO,MAAM;;;;;;;;;;;;;;;;;;kDAK7E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,mNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DACrB,OAAO,QAAQ;4DAAC;4DAAG,OAAO,OAAO;;;;;;;;;;;;;0DAItC,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,kBACzC,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DASX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,+NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;+BAtE3B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;AA+E5B", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/CasinoDetailScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ChevronRightIcon,\n  HeartIcon,\n  ShareIcon,\n  MapPinIcon,\n  SparklesIcon,\n  ClockIcon,\n  CurrencyDollarIcon,\n  UserGroupIcon,\n  BuildingOfficeIcon\n} from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';\nimport { useState } from 'react';\n\ninterface CasinoDetailScreenProps {\n  onNavigate: (screen: string) => void;\n  onBack: () => void;\n}\n\nexport default function CasinoDetailScreen({ onNavigate, onBack }: CasinoDetailScreenProps) {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isFavorite, setIsFavorite] = useState(false);\n\n  const casino = {\n    name: \"Marina Bay Sands\",\n    location: \"Singapore\",\n    country: \"Singapore\",\n    rating: 5,\n    vipLevel: \"Platinum\",\n    description: \"An iconic luxury resort featuring world-class gaming, dining, shopping, and entertainment. Home to the famous infinity pool and SkyPark with breathtaking city views.\",\n    images: [\"/api/placeholder/400/300\", \"/api/placeholder/400/300\", \"/api/placeholder/400/300\"],\n    features: {\n      overview: {\n        openingHours: \"24/7\",\n        dressCode: \"Smart Casual\",\n        minAge: \"21+\",\n        currency: \"SGD\",\n        languages: [\"English\", \"Mandarin\", \"Malay\"],\n      },\n      facilities: [\n        { name: \"VIP Gaming Rooms\", description: \"Private gaming areas with dedicated service\" },\n        { name: \"Infinity Pool\", description: \"World's largest rooftop infinity pool\" },\n        { name: \"SkyPark\", description: \"Observation deck with panoramic views\" },\n        { name: \"Shopping Mall\", description: \"Luxury retail with international brands\" },\n        { name: \"Fine Dining\", description: \"Michelin-starred restaurants\" },\n        { name: \"Theater\", description: \"World-class entertainment shows\" },\n      ],\n      games: [\n        { name: \"Baccarat\", tables: 45, minBet: \"$25\", maxBet: \"$500,000\" },\n        { name: \"Blackjack\", tables: 32, minBet: \"$25\", maxBet: \"$10,000\" },\n        { name: \"Roulette\", tables: 18, minBet: \"$5\", maxBet: \"$50,000\" },\n        { name: \"Poker\", tables: 12, minBet: \"$5/$10\", maxBet: \"$25/$50\" },\n        { name: \"Slot Machines\", tables: 2500, minBet: \"$0.01\", maxBet: \"$1,000\" },\n      ],\n      access: {\n        address: \"10 Bayfront Avenue, Singapore 018956\",\n        transport: [\"MRT: Bayfront Station\", \"Taxi\", \"Private Car\"],\n        parking: \"Valet parking available\",\n        airport: \"15 minutes from Changi Airport\",\n      }\n    }\n  };\n\n  const tabs = [\n    { id: 'overview', label: 'Overview' },\n    { id: 'facilities', label: 'Facilities' },\n    { id: 'games', label: 'Games' },\n    { id: 'access', label: 'Access' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-dark-950 pb-20\">\n      {/* Header with Image */}\n      <div className=\"relative h-80\">\n        <div className=\"absolute inset-0 bg-dark-800\"></div>\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent\"></div>\n        \n        {/* Header Controls */}\n        <motion.div\n          initial={{ y: -50, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.6 }}\n          className=\"absolute top-0 left-0 right-0 z-20 flex items-center justify-between p-6\"\n        >\n          <button \n            onClick={onBack}\n            className=\"w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center\"\n          >\n            <ChevronRightIcon className=\"w-5 h-5 text-white rotate-180\" />\n          </button>\n          <div className=\"flex items-center space-x-3\">\n            <button \n              onClick={() => setIsFavorite(!isFavorite)}\n              className=\"w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center\"\n            >\n              {isFavorite ? (\n                <HeartIconSolid className=\"w-5 h-5 text-red-500\" />\n              ) : (\n                <HeartIcon className=\"w-5 h-5 text-white\" />\n              )}\n            </button>\n            <button className=\"w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center\">\n              <ShareIcon className=\"w-5 h-5 text-white\" />\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Casino Info Overlay */}\n        <motion.div\n          initial={{ y: 30, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"absolute bottom-6 left-6 right-6 z-20\"\n        >\n          <div className=\"flex items-center justify-between mb-2\">\n            <span className=\"px-3 py-1 bg-gray-300/20 backdrop-blur-sm rounded-full text-gray-300 text-xs font-medium\">\n              {casino.vipLevel} VIP\n            </span>\n            <div className=\"flex items-center space-x-1\">\n              {[...Array(casino.rating)].map((_, i) => (\n                <SparklesIcon key={i} className=\"w-4 h-4 text-gold-400\" />\n              ))}\n            </div>\n          </div>\n          <h1 className=\"text-2xl font-serif font-bold text-white mb-1\">\n            {casino.name}\n          </h1>\n          <div className=\"flex items-center text-gold-400\">\n            <MapPinIcon className=\"w-4 h-4 mr-1\" />\n            <span className=\"text-sm\">{casino.location}, {casino.country}</span>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Tab Navigation */}\n      <motion.div\n        initial={{ y: 20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6, delay: 0.4 }}\n        className=\"sticky top-0 z-30 bg-dark-900/95 backdrop-blur-md border-b border-dark-700\"\n      >\n        <div className=\"flex items-center px-6 py-3 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`relative px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors ${\n                activeTab === tab.id ? 'text-gold-400' : 'text-gray-400'\n              }`}\n            >\n              {tab.label}\n              {activeTab === tab.id && (\n                <motion.div\n                  layoutId=\"activeTabIndicator\"\n                  className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gold-400\"\n                  transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                />\n              )}\n            </button>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Tab Content */}\n      <div className=\"px-6 py-6\">\n        {activeTab === 'overview' && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"space-y-6\"\n          >\n            <div>\n              <h3 className=\"text-lg font-serif font-semibold text-white mb-3\">About</h3>\n              <p className=\"text-gray-400 leading-relaxed\">{casino.description}</p>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"card-luxury p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <ClockIcon className=\"w-5 h-5 text-gold-400 mr-2\" />\n                  <span className=\"text-white font-medium\">Hours</span>\n                </div>\n                <p className=\"text-gray-400 text-sm\">{casino.features.overview.openingHours}</p>\n              </div>\n\n              <div className=\"card-luxury p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <UserGroupIcon className=\"w-5 h-5 text-gold-400 mr-2\" />\n                  <span className=\"text-white font-medium\">Dress Code</span>\n                </div>\n                <p className=\"text-gray-400 text-sm\">{casino.features.overview.dressCode}</p>\n              </div>\n\n              <div className=\"card-luxury p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <CurrencyDollarIcon className=\"w-5 h-5 text-gold-400 mr-2\" />\n                  <span className=\"text-white font-medium\">Currency</span>\n                </div>\n                <p className=\"text-gray-400 text-sm\">{casino.features.overview.currency}</p>\n              </div>\n\n              <div className=\"card-luxury p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <BuildingOfficeIcon className=\"w-5 h-5 text-gold-400 mr-2\" />\n                  <span className=\"text-white font-medium\">Min Age</span>\n                </div>\n                <p className=\"text-gray-400 text-sm\">{casino.features.overview.minAge}</p>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {activeTab === 'facilities' && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"space-y-4\"\n          >\n            {casino.features.facilities.map((facility, index) => (\n              <div key={index} className=\"card-luxury p-4\">\n                <h4 className=\"text-white font-semibold mb-2\">{facility.name}</h4>\n                <p className=\"text-gray-400 text-sm\">{facility.description}</p>\n              </div>\n            ))}\n          </motion.div>\n        )}\n\n        {activeTab === 'games' && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"space-y-4\"\n          >\n            {casino.features.games.map((game, index) => (\n              <div key={index} className=\"card-luxury p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h4 className=\"text-white font-semibold\">{game.name}</h4>\n                  <span className=\"text-gold-400 text-sm\">{game.tables} {game.name === 'Slot Machines' ? 'machines' : 'tables'}</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-400\">Min: {game.minBet}</span>\n                  <span className=\"text-gray-400\">Max: {game.maxBet}</span>\n                </div>\n              </div>\n            ))}\n          </motion.div>\n        )}\n\n        {activeTab === 'access' && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"space-y-6\"\n          >\n            <div className=\"card-luxury p-4\">\n              <h4 className=\"text-white font-semibold mb-3\">Address</h4>\n              <p className=\"text-gray-400 text-sm\">{casino.features.access.address}</p>\n            </div>\n\n            <div className=\"card-luxury p-4\">\n              <h4 className=\"text-white font-semibold mb-3\">Transportation</h4>\n              <div className=\"space-y-2\">\n                {casino.features.access.transport.map((option, index) => (\n                  <p key={index} className=\"text-gray-400 text-sm\">• {option}</p>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"card-luxury p-4\">\n              <h4 className=\"text-white font-semibold mb-3\">Additional Info</h4>\n              <p className=\"text-gray-400 text-sm mb-2\">{casino.features.access.parking}</p>\n              <p className=\"text-gray-400 text-sm\">{casino.features.access.airport}</p>\n            </div>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Inquiry Button */}\n      <motion.div\n        initial={{ y: 50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6, delay: 0.6 }}\n        className=\"fixed bottom-20 left-6 right-6 z-40\"\n      >\n        <button \n          onClick={() => onNavigate('inquiry')}\n          className=\"w-full btn-luxury text-center\"\n        >\n          Inquire About This Casino\n        </button>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAfA;;;;;;AAsBe,SAAS,mBAAmB,EAAE,UAAU,EAAE,MAAM,EAA2B;IACxF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,SAAS;QACb,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,UAAU;QACV,aAAa;QACb,QAAQ;YAAC;YAA4B;YAA4B;SAA2B;QAC5F,UAAU;YACR,UAAU;gBACR,cAAc;gBACd,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,WAAW;oBAAC;oBAAW;oBAAY;iBAAQ;YAC7C;YACA,YAAY;gBACV;oBAAE,MAAM;oBAAoB,aAAa;gBAA8C;gBACvF;oBAAE,MAAM;oBAAiB,aAAa;gBAAwC;gBAC9E;oBAAE,MAAM;oBAAW,aAAa;gBAAwC;gBACxE;oBAAE,MAAM;oBAAiB,aAAa;gBAA0C;gBAChF;oBAAE,MAAM;oBAAe,aAAa;gBAA+B;gBACnE;oBAAE,MAAM;oBAAW,aAAa;gBAAkC;aACnE;YACD,OAAO;gBACL;oBAAE,MAAM;oBAAY,QAAQ;oBAAI,QAAQ;oBAAO,QAAQ;gBAAW;gBAClE;oBAAE,MAAM;oBAAa,QAAQ;oBAAI,QAAQ;oBAAO,QAAQ;gBAAU;gBAClE;oBAAE,MAAM;oBAAY,QAAQ;oBAAI,QAAQ;oBAAM,QAAQ;gBAAU;gBAChE;oBAAE,MAAM;oBAAS,QAAQ;oBAAI,QAAQ;oBAAU,QAAQ;gBAAU;gBACjE;oBAAE,MAAM;oBAAiB,QAAQ;oBAAM,QAAQ;oBAAS,QAAQ;gBAAS;aAC1E;YACD,QAAQ;gBACN,SAAS;gBACT,WAAW;oBAAC;oBAAyB;oBAAQ;iBAAc;gBAC3D,SAAS;gBACT,SAAS;YACX;QACF;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAc,OAAO;QAAa;QACxC;YAAE,IAAI;YAAS,OAAO;QAAQ;QAC9B;YAAE,IAAI;YAAU,OAAO;QAAS;KACjC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BACC,8OAAC,+MAAA,CAAA,YAAc;4CAAC,WAAU;;;;;iEAE1B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAGzB,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,OAAO,QAAQ;4CAAC;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM,OAAO,MAAM;yCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,uNAAA,CAAA,eAAY;gDAAS,WAAU;+CAAb;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAG,WAAU;0CACX,OAAO,IAAI;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;;4CAAW,OAAO,QAAQ;4CAAC;4CAAG,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,2EAA2E,EACrF,cAAc,IAAI,EAAE,GAAG,kBAAkB,iBACzC;;gCAED,IAAI,KAAK;gCACT,cAAc,IAAI,EAAE,kBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;;;;;;;2BAXzD,IAAI,EAAE;;;;;;;;;;;;;;;0BAoBnB,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDACjE,8OAAC;wCAAE,WAAU;kDAAiC,OAAO,WAAW;;;;;;;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;0DAE3C,8OAAC;gDAAE,WAAU;0DAAyB,OAAO,QAAQ,CAAC,QAAQ,CAAC,YAAY;;;;;;;;;;;;kDAG7E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;0DAE3C,8OAAC;gDAAE,WAAU;0DAAyB,OAAO,QAAQ,CAAC,QAAQ,CAAC,SAAS;;;;;;;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,mOAAA,CAAA,qBAAkB;wDAAC,WAAU;;;;;;kEAC9B,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;0DAE3C,8OAAC;gDAAE,WAAU;0DAAyB,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,mOAAA,CAAA,qBAAkB;wDAAC,WAAU;;;;;;kEAC9B,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;0DAE3C,8OAAC;gDAAE,WAAU;0DAAyB,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;oBAM5E,cAAc,8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACzC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;kDAAiC,SAAS,IAAI;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAyB,SAAS,WAAW;;;;;;;+BAFlD;;;;;;;;;;oBAQf,cAAc,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4B,KAAK,IAAI;;;;;;0DACnD,8OAAC;gDAAK,WAAU;;oDAAyB,KAAK,MAAM;oDAAC;oDAAE,KAAK,IAAI,KAAK,kBAAkB,aAAa;;;;;;;;;;;;;kDAEtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAAgB;oDAAM,KAAK,MAAM;;;;;;;0DACjD,8OAAC;gDAAK,WAAU;;oDAAgB;oDAAM,KAAK,MAAM;;;;;;;;;;;;;;+BAP3C;;;;;;;;;;oBAcf,cAAc,0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;kDAAyB,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7C,8OAAC;gDAAc,WAAU;;oDAAwB;oDAAG;;+CAA5C;;;;;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;kDAA8B,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;;;;;;kDACzE,8OAAC;wCAAE,WAAU;kDAAyB,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC;oBACC,SAAS,IAAM,WAAW;oBAC1B,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/ChatScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  ChevronRightIcon,\n  PaperAirplaneIcon,\n  SparklesIcon\n} from '@heroicons/react/24/outline';\nimport { useState, useRef, useEffect } from 'react';\n\ninterface ChatScreenProps {\n  onBack: () => void;\n}\n\ninterface Message {\n  id: string;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  typing?: boolean;\n}\n\nexport default function ChatScreen({ onBack }: ChatScreenProps) {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      text: \"Welcome to VIP Access Concierge! I'm your personal AI assistant for luxury casino experiences. How may I assist you today?\",\n      isUser: false,\n      timestamp: new Date(),\n    },\n    {\n      id: '2',\n      text: \"I can help you with:\\n• Casino recommendations\\n• VIP services and amenities\\n• Booking assistance\\n• Dress codes and etiquette\\n• Gaming information\",\n      isUser: false,\n      timestamp: new Date(),\n    }\n  ]);\n  const [inputText, setInputText] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const quickQuestions = [\n    \"What's the dress code?\",\n    \"Best casinos in Singapore?\",\n    \"VIP room availability?\",\n    \"Minimum betting limits?\",\n  ];\n\n  const aiResponses = [\n    \"For most premium casinos, smart casual attire is required. Men should wear collared shirts and dress shoes, while women should opt for elegant dresses or business attire. Some VIP areas may require formal wear.\",\n    \"Singapore offers world-class gaming at Marina Bay Sands and Resorts World Sentosa. Both feature exclusive VIP gaming areas, luxury accommodations, and exceptional dining experiences.\",\n    \"VIP rooms are available by reservation and typically require minimum gaming commitments. I can connect you with our concierge team to check availability and arrange your exclusive gaming experience.\",\n    \"Minimum bets vary by casino and game type. Table games typically start from $25-$100, while VIP rooms may require $500+ minimums. Slot machines can start as low as $1 per spin.\",\n    \"I'd be happy to help you find the perfect casino experience! Could you tell me more about your preferences - are you looking for a specific location, game type, or particular amenities?\",\n    \"Our VIP services include private gaming rooms, dedicated hosts, complimentary dining, luxury transportation, and exclusive event access. Would you like me to arrange a consultation with our concierge team?\"\n  ];\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = async (text: string) => {\n    if (!text.trim()) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      text: text.trim(),\n      isUser: true,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputText('');\n    setIsTyping(true);\n\n    // Simulate AI response delay\n    setTimeout(() => {\n      const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];\n      const aiMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        text: randomResponse,\n        isUser: false,\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev, aiMessage]);\n      setIsTyping(false);\n    }, 1500 + Math.random() * 1000);\n  };\n\n  const handleQuickQuestion = (question: string) => {\n    handleSendMessage(question);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-dark-950 flex flex-col\">\n      {/* Header */}\n      <motion.header\n        initial={{ y: -50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className=\"sticky top-0 z-40 px-6 py-4 bg-dark-900/95 backdrop-blur-md border-b border-dark-700\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <button \n            onClick={onBack}\n            className=\"w-10 h-10 bg-dark-800 rounded-full flex items-center justify-center\"\n          >\n            <ChevronRightIcon className=\"w-5 h-5 text-gold-400 rotate-180\" />\n          </button>\n          \n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-gold-500 to-gold-700 rounded-full flex items-center justify-center\">\n              <SparklesIcon className=\"w-5 h-5 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-serif font-semibold text-white\">AI Concierge</h1>\n              <div className=\"flex items-center\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"></div>\n                <span className=\"text-xs text-green-400\">Online</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"w-10\"></div>\n        </div>\n      </motion.header>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto px-6 py-6 space-y-4\">\n        <AnimatePresence>\n          {messages.map((message, index) => (\n            <motion.div\n              key={message.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`max-w-[80%] ${message.isUser ? 'order-2' : 'order-1'}`}>\n                {!message.isUser && (\n                  <div className=\"flex items-center mb-2\">\n                    <div className=\"w-6 h-6 bg-gradient-to-br from-gold-500 to-gold-700 rounded-full flex items-center justify-center mr-2\">\n                      <SparklesIcon className=\"w-3 h-3 text-white\" />\n                    </div>\n                    <span className=\"text-xs text-gray-400\">AI Concierge</span>\n                  </div>\n                )}\n                \n                <div\n                  className={`px-4 py-3 rounded-2xl ${\n                    message.isUser\n                      ? 'bg-gradient-to-r from-gold-600 to-gold-500 text-white'\n                      : 'bg-dark-800 text-gray-100 border border-dark-700'\n                  }`}\n                >\n                  <p className=\"text-sm leading-relaxed whitespace-pre-line\">\n                    {message.text}\n                  </p>\n                </div>\n                \n                <div className={`mt-1 ${message.isUser ? 'text-right' : 'text-left'}`}>\n                  <span className=\"text-xs text-gray-500\">\n                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </span>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n\n        {/* Typing Indicator */}\n        {isTyping && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"flex justify-start\"\n          >\n            <div className=\"max-w-[80%]\">\n              <div className=\"flex items-center mb-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-br from-gold-500 to-gold-700 rounded-full flex items-center justify-center mr-2\">\n                  <SparklesIcon className=\"w-3 h-3 text-white\" />\n                </div>\n                <span className=\"text-xs text-gray-400\">AI Concierge is typing...</span>\n              </div>\n              <div className=\"bg-dark-800 border border-dark-700 rounded-2xl px-4 py-3\">\n                <div className=\"flex space-x-1\">\n                  {[0, 1, 2].map((i) => (\n                    <motion.div\n                      key={i}\n                      animate={{\n                        scale: [1, 1.2, 1],\n                        opacity: [0.5, 1, 0.5],\n                      }}\n                      transition={{\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: i * 0.2,\n                      }}\n                      className=\"w-2 h-2 bg-gold-400 rounded-full\"\n                    />\n                  ))}\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Quick Questions */}\n      {messages.length <= 2 && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          className=\"px-6 py-4\"\n        >\n          <p className=\"text-gray-400 text-sm mb-3\">Quick questions:</p>\n          <div className=\"flex flex-wrap gap-2\">\n            {quickQuestions.map((question, index) => (\n              <button\n                key={index}\n                onClick={() => handleQuickQuestion(question)}\n                className=\"px-3 py-2 bg-dark-800 border border-dark-700 rounded-lg text-gray-300 text-sm hover:border-gold-500 transition-colors\"\n              >\n                {question}\n              </button>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Input Area */}\n      <motion.div\n        initial={{ y: 50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className=\"sticky bottom-0 px-6 py-4 bg-dark-900/95 backdrop-blur-md border-t border-dark-700\"\n      >\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex-1 relative\">\n            <input\n              type=\"text\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputText)}\n              placeholder=\"Ask me anything about casinos...\"\n              className=\"w-full px-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-gold-500 transition-colors\"\n              disabled={isTyping}\n            />\n          </div>\n          <button\n            onClick={() => handleSendMessage(inputText)}\n            disabled={!inputText.trim() || isTyping}\n            className=\"w-12 h-12 bg-gradient-to-r from-gold-600 to-gold-500 rounded-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-gold\"\n          >\n            <PaperAirplaneIcon className=\"w-5 h-5 text-white\" />\n          </button>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAKA;AARA;;;;;AAsBe,SAAS,WAAW,EAAE,MAAM,EAAmB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,KAAK,IAAI,IAAI;QAElB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,aAAa;QACb,YAAY;QAEZ,6BAA6B;QAC7B,WAAW;YACT,MAAM,iBAAiB,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;YAClF,MAAM,YAAqB;gBACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YACxC,YAAY;QACd,GAAG,OAAO,KAAK,MAAM,KAAK;IAC5B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;sCAG9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yLAAA,CAAA,kBAAe;kCACb,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;0CAErE,cAAA,8OAAC;oCAAI,WAAW,CAAC,YAAY,EAAE,QAAQ,MAAM,GAAG,YAAY,WAAW;;wCACpE,CAAC,QAAQ,MAAM,kBACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAI5C,8OAAC;4CACC,WAAW,CAAC,sBAAsB,EAChC,QAAQ,MAAM,GACV,0DACA,oDACJ;sDAEF,cAAA,8OAAC;gDAAE,WAAU;0DACV,QAAQ,IAAI;;;;;;;;;;;sDAIjB,8OAAC;4CAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,eAAe,aAAa;sDACnE,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;oDAAE,MAAM;oDAAW,QAAQ;gDAAU;;;;;;;;;;;;;;;;;+BA9BhF,QAAQ,EAAE;;;;;;;;;;oBAuCpB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAG;qDAAI;gDACxB;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,OAAO,IAAI;gDACb;gDACA,WAAU;+CAVL;;;;;;;;;;;;;;;;;;;;;;;;;;kCAmBnB,8OAAC;wBAAI,KAAK;;;;;;;;;;;;YAIX,SAAS,MAAM,IAAI,mBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET;+BAJI;;;;;;;;;;;;;;;;0BAYf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,kBAAkB;gCAC1D,aAAY;gCACZ,WAAU;gCACV,UAAU;;;;;;;;;;;sCAGd,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,UAAU,CAAC,UAAU,IAAI,MAAM;4BAC/B,WAAU;sCAEV,cAAA,8OAAC,iOAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 2787, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/InquiryScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ChevronRightIcon,\n  UserIcon,\n  EnvelopeIcon,\n  PhoneIcon,\n  CalendarIcon,\n  ChatBubbleLeftRightIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { useState } from 'react';\n\ninterface InquiryScreenProps {\n  onBack: () => void;\n}\n\nexport default function InquiryScreen({ onBack }: InquiryScreenProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    casino: '',\n    preferredDate: '',\n    inquiry: '',\n    privacyAccepted: false,\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const casinos = [\n    'Marina Bay Sands',\n    'The Venetian Macao',\n    'Crown Melbourne',\n    'City of Dreams',\n    'Resorts World Sentosa',\n    'Wynn Palace',\n    'Other / General Inquiry'\n  ];\n\n  const handleInputChange = (field: string, value: string | boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!formData.name || !formData.email || !formData.inquiry || !formData.privacyAccepted) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setIsSubmitted(true);\n    }, 2000);\n  };\n\n  if (isSubmitted) {\n    return (\n      <div className=\"min-h-screen bg-dark-950 flex items-center justify-center px-6\">\n        <motion.div\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            className=\"w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6\"\n          >\n            <CheckCircleIcon className=\"w-10 h-10 text-white\" />\n          </motion.div>\n          \n          <motion.div\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <h2 className=\"text-2xl font-serif font-bold text-white mb-4\">\n              Inquiry Submitted Successfully!\n            </h2>\n            <p className=\"text-gray-400 mb-8 leading-relaxed\">\n              Thank you for your interest. Our dedicated concierge team will review your inquiry and contact you within 24 hours to arrange your exclusive casino experience.\n            </p>\n            <button \n              onClick={onBack}\n              className=\"btn-luxury\"\n            >\n              Return to Home\n            </button>\n          </motion.div>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-dark-950 pb-20\">\n      {/* Header */}\n      <motion.header\n        initial={{ y: -50, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className=\"sticky top-0 z-40 px-6 py-4 bg-dark-900/95 backdrop-blur-md border-b border-dark-700\"\n      >\n        <div className=\"flex items-center justify-between\">\n          <button \n            onClick={onBack}\n            className=\"w-10 h-10 bg-dark-800 rounded-full flex items-center justify-center\"\n          >\n            <ChevronRightIcon className=\"w-5 h-5 text-gold-400 rotate-180\" />\n          </button>\n          <h1 className=\"text-xl font-serif font-semibold text-white\">Contact Concierge</h1>\n          <div className=\"w-10\"></div>\n        </div>\n      </motion.header>\n\n      {/* Form */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.2 }}\n        className=\"px-6 py-6\"\n      >\n        {/* Introduction */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-serif font-bold text-white mb-4\">\n            Exclusive VIP Experience Awaits\n          </h2>\n          <p className=\"text-gray-400 leading-relaxed\">\n            Our dedicated concierge team will personally assist you in arranging your luxury casino experience. Please provide your details below, and we'll contact you within 24 hours.\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Name Field */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Full Name *\n            </label>\n            <div className=\"relative\">\n              <UserIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                placeholder=\"Enter your full name\"\n                className=\"w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-gold-500 transition-colors\"\n                required\n              />\n            </div>\n          </motion.div>\n\n          {/* Email Field */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Email Address *\n            </label>\n            <div className=\"relative\">\n              <EnvelopeIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                placeholder=\"Enter your email address\"\n                className=\"w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-gold-500 transition-colors\"\n                required\n              />\n            </div>\n          </motion.div>\n\n          {/* Phone Field */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.5 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Phone Number\n            </label>\n            <div className=\"relative\">\n              <PhoneIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={formData.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                placeholder=\"Enter your phone number\"\n                className=\"w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-gold-500 transition-colors\"\n              />\n            </div>\n          </motion.div>\n\n          {/* Casino Selection */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.6 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Interested Casino\n            </label>\n            <select\n              value={formData.casino}\n              onChange={(e) => handleInputChange('casino', e.target.value)}\n              className=\"w-full px-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white focus:outline-none focus:border-gold-500 transition-colors\"\n            >\n              <option value=\"\">Select a casino</option>\n              {casinos.map((casino) => (\n                <option key={casino} value={casino}>{casino}</option>\n              ))}\n            </select>\n          </motion.div>\n\n          {/* Preferred Date */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.7 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Preferred Visit Date\n            </label>\n            <div className=\"relative\">\n              <CalendarIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"date\"\n                value={formData.preferredDate}\n                onChange={(e) => handleInputChange('preferredDate', e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white focus:outline-none focus:border-gold-500 transition-colors\"\n              />\n            </div>\n          </motion.div>\n\n          {/* Inquiry Message */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.8 }}\n          >\n            <label className=\"block text-white font-medium mb-2\">\n              Your Inquiry *\n            </label>\n            <div className=\"relative\">\n              <ChatBubbleLeftRightIcon className=\"absolute left-3 top-3 w-5 h-5 text-gray-400\" />\n              <textarea\n                value={formData.inquiry}\n                onChange={(e) => handleInputChange('inquiry', e.target.value)}\n                placeholder=\"Please describe your requirements, preferences, or any specific requests...\"\n                rows={4}\n                className=\"w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-gold-500 transition-colors resize-none\"\n                required\n              />\n            </div>\n          </motion.div>\n\n          {/* Privacy Policy */}\n          <motion.div\n            initial={{ x: -20, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.9 }}\n            className=\"flex items-start space-x-3\"\n          >\n            <input\n              type=\"checkbox\"\n              id=\"privacy\"\n              checked={formData.privacyAccepted}\n              onChange={(e) => handleInputChange('privacyAccepted', e.target.checked)}\n              className=\"mt-1 w-4 h-4 text-gold-500 bg-dark-800 border-dark-700 rounded focus:ring-gold-500 focus:ring-2\"\n              required\n            />\n            <label htmlFor=\"privacy\" className=\"text-sm text-gray-400 leading-relaxed\">\n              I agree to the privacy policy and consent to the collection and processing of my personal data for the purpose of providing VIP casino concierge services.\n            </label>\n          </motion.div>\n\n          {/* Submit Button */}\n          <motion.div\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.5, delay: 1 }}\n            className=\"pt-4\"\n          >\n            <button\n              type=\"submit\"\n              disabled={isSubmitting || !formData.name || !formData.email || !formData.inquiry || !formData.privacyAccepted}\n              className=\"w-full btn-luxury disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                  Submitting...\n                </div>\n              ) : (\n                'Submit Inquiry'\n              )}\n            </button>\n          </motion.div>\n        </form>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAZA;;;;;AAkBe,SAAS,cAAc,EAAE,MAAM,EAAsB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;QACR,eAAe;QACf,SAAS;QACT,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,eAAe,EAAE;YACvF;QACF;QAEA,gBAAgB;QAEhB,2BAA2B;QAC3B,WAAW;YACT,gBAAgB;YAChB,eAAe;QACjB,GAAG;IACL;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,6NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAG7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;sCAE9B,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAK/C,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCACC,OAAO,SAAS,MAAM;wCACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC3D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oDAAoB,OAAO;8DAAS;mDAAxB;;;;;;;;;;;;;;;;;0CAMnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6OAAA,CAAA,0BAAuB;gDAAC,WAAU;;;;;;0DACnC,8OAAC;gDACC,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC5D,aAAY;gDACZ,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;gCAAE;gCAC9B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,SAAS,eAAe;wCACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,OAAO;wCACtE,WAAU;wCACV,QAAQ;;;;;;kDAEV,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAAwC;;;;;;;;;;;;0CAM7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAE;gCACtC,WAAU;0CAEV,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU,gBAAgB,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,eAAe;oCAC7G,WAAU;8CAET,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;;;;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 3490, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/components/BottomNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport {\n  HomeIcon,\n  BuildingOffice2Icon,\n  ChatBubbleLeftRightIcon,\n  UserIcon\n} from '@heroicons/react/24/outline';\nimport {\n  HomeIcon as HomeIconSolid,\n  BuildingOffice2Icon as BuildingOffice2IconSolid,\n  ChatBubbleLeftRightIcon as ChatBubbleLeftRightIconSolid,\n  UserIcon as UserIconSolid\n} from '@heroicons/react/24/solid';\n\ninterface BottomNavigationProps {\n  activeScreen: string;\n  onNavigate: (screen: string) => void;\n}\n\nexport default function BottomNavigation({ activeScreen, onNavigate }: BottomNavigationProps) {\n  const navItems = [\n    {\n      id: 'home',\n      label: 'Home',\n      icon: HomeIcon,\n      iconSolid: HomeIconSolid,\n    },\n    {\n      id: 'casinos',\n      label: 'Casinos',\n      icon: BuildingOffice2Icon,\n      iconSolid: BuildingOffice2IconSolid,\n    },\n    {\n      id: 'chat',\n      label: 'Chat',\n      icon: ChatBubbleLeftRightIcon,\n      iconSolid: ChatBubbleLeftRightIconSolid,\n      badge: true,\n    },\n    {\n      id: 'profile',\n      label: 'Profile',\n      icon: UserIcon,\n      iconSolid: UserIconSolid,\n    },\n  ];\n\n  return (\n    <motion.div\n      initial={{ y: 100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"fixed bottom-0 left-0 right-0 z-50\"\n    >\n      <div className=\"bg-gray-900/95 backdrop-blur-md border-t border-gray-700 px-4 py-2\">\n        <div className=\"flex items-center justify-around\">\n          {navItems.map((item) => {\n            const isActive = activeScreen === item.id;\n            const Icon = isActive ? item.iconSolid : item.icon;\n\n            return (\n              <motion.button\n                key={item.id}\n                onClick={() => onNavigate(item.id)}\n                className=\"relative flex flex-col items-center py-2 px-3 min-w-[60px]\"\n                whileTap={{ scale: 0.95 }}\n              >\n                {/* Active indicator */}\n                {isActive && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-yellow-500 rounded-full\"\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  />\n                )}\n\n                {/* Icon container */}\n                <div className=\"relative\">\n                  <Icon\n                    className={`w-6 h-6 transition-colors duration-200 ${\n                      isActive ? 'text-yellow-400' : 'text-gray-400'\n                    }`}\n                  />\n\n                  {/* Badge */}\n                  {item.badge && (\n                    <motion.div\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center\"\n                    >\n                      <span className=\"text-white text-xs\">1</span>\n                    </motion.div>\n                  )}\n                </div>\n\n                {/* Label */}\n                <span\n                  className={`text-xs mt-1 transition-colors duration-200 ${\n                    isActive ? 'text-yellow-400 font-medium' : 'text-gray-400'\n                  }`}\n                >\n                  {item.label}\n                </span>\n\n                {/* Ripple effect */}\n                {isActive && (\n                  <motion.div\n                    initial={{ scale: 0, opacity: 0.5 }}\n                    animate={{ scale: 1.5, opacity: 0 }}\n                    transition={{ duration: 0.6 }}\n                    className=\"absolute inset-0 bg-yellow-400/20 rounded-full\"\n                  />\n                )}\n              </motion.button>\n            );\n          })}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AATA;;;;;AAqBe,SAAS,iBAAiB,EAAE,YAAY,EAAE,UAAU,EAAyB;IAC1F,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,MAAM,+MAAA,CAAA,WAAQ;YACd,WAAW,6MAAA,CAAA,WAAa;QAC1B;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qOAAA,CAAA,sBAAmB;YACzB,WAAW,mOAAA,CAAA,sBAAwB;QACrC;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6OAAA,CAAA,0BAAuB;YAC7B,WAAW,2OAAA,CAAA,0BAA4B;YACvC,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,+MAAA,CAAA,WAAQ;YACd,WAAW,6MAAA,CAAA,WAAa;QAC1B;KACD;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG;QAAI;QAClB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC;oBACb,MAAM,WAAW,iBAAiB,KAAK,EAAE;oBACzC,MAAM,OAAO,WAAW,KAAK,SAAS,GAAG,KAAK,IAAI;oBAElD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS,IAAM,WAAW,KAAK,EAAE;wBACjC,WAAU;wBACV,UAAU;4BAAE,OAAO;wBAAK;;4BAGvB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAS;gCACT,WAAU;gCACV,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;;;;;;0CAK9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC,uCAAuC,EACjD,WAAW,oBAAoB,iBAC/B;;;;;;oCAIH,KAAK,KAAK,kBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCACC,WAAW,CAAC,4CAA4C,EACtD,WAAW,gCAAgC,iBAC3C;0CAED,KAAK,KAAK;;;;;;4BAIZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAI;gCAClC,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;;;;;;uBAjDT,KAAK,EAAE;;;;;gBAsDlB;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 3666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/casino%20-%20app/vip-casino-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport SplashScreen from '@/components/SplashScreen';\nimport HomeScreen from '@/components/HomeScreen';\nimport CasinoListScreen from '@/components/CasinoListScreen';\nimport CasinoDetailScreen from '@/components/CasinoDetailScreen';\nimport ChatScreen from '@/components/ChatScreen';\nimport InquiryScreen from '@/components/InquiryScreen';\nimport BottomNavigation from '@/components/BottomNavigation';\n\nexport default function Home() {\n  const [currentScreen, setCurrentScreen] = useState<string>('splash');\n  const [showSplash, setShowSplash] = useState(true);\n\n  const handleSplashComplete = () => {\n    setShowSplash(false);\n    setCurrentScreen('home');\n  };\n\n  const handleNavigate = (screen: string) => {\n    setCurrentScreen(screen);\n  };\n\n  const handleBack = () => {\n    setCurrentScreen('home');\n  };\n\n  if (showSplash) {\n    return <SplashScreen onComplete={handleSplashComplete} />;\n  }\n\n  const renderScreen = () => {\n    switch (currentScreen) {\n      case 'home':\n        return <HomeScreen onNavigate={handleNavigate} />;\n      case 'casinos':\n        return <CasinoListScreen onNavigate={handleNavigate} onBack={handleBack} />;\n      case 'casino-detail':\n        return <CasinoDetailScreen onNavigate={handleNavigate} onBack={handleBack} />;\n      case 'chat':\n        return <ChatScreen onBack={handleBack} />;\n      case 'inquiry':\n        return <InquiryScreen onBack={handleBack} />;\n      case 'profile':\n        return (\n          <div className=\"min-h-screen bg-dark-950 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-serif text-white mb-4\">Profile</h2>\n              <p className=\"text-gray-400\">Coming Soon</p>\n            </div>\n          </div>\n        );\n      default:\n        return <HomeScreen onNavigate={handleNavigate} />;\n    }\n  };\n\n  const showBottomNav = !['splash', 'casino-detail', 'chat', 'inquiry'].includes(currentScreen);\n\n  return (\n    <div className=\"relative\">\n      {renderScreen()}\n      {showBottomNav && (\n        <BottomNavigation\n          activeScreen={currentScreen}\n          onNavigate={handleNavigate}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB;QAC3B,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB;IACnB;IAEA,MAAM,aAAa;QACjB,iBAAiB;IACnB;IAEA,IAAI,YAAY;QACd,qBAAO,8OAAC,kIAAA,CAAA,UAAY;YAAC,YAAY;;;;;;IACnC;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBA<PERSON>,qBAAO,8OAAC,gIAAA,CAAA,UAAU;oBAAC,YAAY;;;;;;YACjC,KAAK;gBACH,qBAAO,8OAAC,sIAAA,CAAA,UAAgB;oBAAC,YAAY;oBAAgB,QAAQ;;;;;;YAC/D,KAAK;gBACH,qBAAO,8OAAC,wIAAA,CAAA,UAAkB;oBAAC,YAAY;oBAAgB,QAAQ;;;;;;YACjE,KAAK;gBACH,qBAAO,8OAAC,gIAAA,CAAA,UAAU;oBAAC,QAAQ;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,mIAAA,CAAA,UAAa;oBAAC,QAAQ;;;;;;YAChC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;YAIrC;gBACE,qBAAO,8OAAC,gIAAA,CAAA,UAAU;oBAAC,YAAY;;;;;;QACnC;IACF;IAEA,MAAM,gBAAgB,CAAC;QAAC;QAAU;QAAiB;QAAQ;KAAU,CAAC,QAAQ,CAAC;IAE/E,qBACE,8OAAC;QAAI,WAAU;;YACZ;YACA,+BACC,8OAAC,sIAAA,CAAA,UAAgB;gBACf,cAAc;gBACd,YAAY;;;;;;;;;;;;AAKtB", "debugId": null}}]}