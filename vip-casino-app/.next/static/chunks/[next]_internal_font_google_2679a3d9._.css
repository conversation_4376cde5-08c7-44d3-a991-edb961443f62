/* [next]/internal/font/google/playfair_display_4d3ec217.module.css [app-client] (css) */
@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ-s.9374b1c2.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ-s.1c293666.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ-s.ab1d62f6.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ-s.p.d4229eda.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ-s.9374b1c2.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ-s.1c293666.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ-s.ab1d62f6.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ-s.p.d4229eda.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ-s.9374b1c2.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ-s.1c293666.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ-s.ab1d62f6.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ-s.p.d4229eda.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ-s.9374b1c2.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ-s.1c293666.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ-s.ab1d62f6.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/nuFiD_vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ-s.p.d4229eda.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display Fallback;
  src: local(Times New Roman);
  ascent-override: 97.25%;
  descent-override: 22.56%;
  line-gap-override: 0.0%;
  size-adjust: 111.26%;
}

.playfair_display_4d3ec217-module__88KSaa__className {
  font-family: Playfair Display, Playfair Display Fallback;
  font-style: normal;
}

.playfair_display_4d3ec217-module__88KSaa__variable {
  --font-playfair: "Playfair Display", "Playfair Display Fallback";
}


/* [next]/internal/font/google/montserrat_5d368e2d.module.css [app-client] (css) */
@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WRhyyTh89ZNpQ-s.5f9b317b.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459W1hyyTh89ZNpQ-s.133d2d48.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WZhyyTh89ZNpQ-s.20038c3d.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ-s.1ecdab47.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y-s.p.d1a74dd0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WRhyyTh89ZNpQ-s.5f9b317b.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459W1hyyTh89ZNpQ-s.133d2d48.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WZhyyTh89ZNpQ-s.20038c3d.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ-s.1ecdab47.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y-s.p.d1a74dd0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WRhyyTh89ZNpQ-s.5f9b317b.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459W1hyyTh89ZNpQ-s.133d2d48.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WZhyyTh89ZNpQ-s.20038c3d.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ-s.1ecdab47.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y-s.p.d1a74dd0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WRhyyTh89ZNpQ-s.5f9b317b.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459W1hyyTh89ZNpQ-s.133d2d48.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WZhyyTh89ZNpQ-s.20038c3d.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ-s.1ecdab47.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y-s.p.d1a74dd0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WRhyyTh89ZNpQ-s.5f9b317b.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459W1hyyTh89ZNpQ-s.133d2d48.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WZhyyTh89ZNpQ-s.20038c3d.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ-s.1ecdab47.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y-s.p.d1a74dd0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Montserrat Fallback;
  src: local(Arial);
  ascent-override: 85.79%;
  descent-override: 22.25%;
  line-gap-override: 0.0%;
  size-adjust: 112.83%;
}

.montserrat_5d368e2d-module__8ycZ0G__className {
  font-family: Montserrat, Montserrat Fallback;
  font-style: normal;
}

.montserrat_5d368e2d-module__8ycZ0G__variable {
  --font-montserrat: "Montserrat", "Montserrat Fallback";
}


/* [next]/internal/font/google/cormorant_garamond_7b626b6c.module.css [app-client] (css) */
@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpHtKky2F7i6C-s.5a23bc52.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYrXtKky2F7i6C-s.2c557612.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpntKky2F7i6C-s.1cfd6b76.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYp3tKky2F7i6C-s.8e811b48.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYqXtKky2F7g-s.p.ce5824b7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpHtKky2F7i6C-s.5a23bc52.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYrXtKky2F7i6C-s.2c557612.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpntKky2F7i6C-s.1cfd6b76.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYp3tKky2F7i6C-s.8e811b48.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYqXtKky2F7g-s.p.ce5824b7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpHtKky2F7i6C-s.5a23bc52.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYrXtKky2F7i6C-s.2c557612.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpntKky2F7i6C-s.1cfd6b76.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYp3tKky2F7i6C-s.8e811b48.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYqXtKky2F7g-s.p.ce5824b7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpHtKky2F7i6C-s.5a23bc52.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYrXtKky2F7i6C-s.2c557612.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpntKky2F7i6C-s.1cfd6b76.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYp3tKky2F7i6C-s.8e811b48.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYqXtKky2F7g-s.p.ce5824b7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpHtKky2F7i6C-s.5a23bc52.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYrXtKky2F7i6C-s.2c557612.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYpntKky2F7i6C-s.1cfd6b76.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYp3tKky2F7i6C-s.8e811b48.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Cormorant Garamond;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/co3bmX5slCNuHLi8bLeY9MK7whWMhyjYqXtKky2F7g-s.p.ce5824b7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Cormorant Garamond Fallback;
  src: local(Times New Roman);
  ascent-override: 95.27%;
  descent-override: 29.59%;
  line-gap-override: 0.0%;
  size-adjust: 96.98%;
}

.cormorant_garamond_7b626b6c-module__p-a56W__className {
  font-family: Cormorant Garamond, Cormorant Garamond Fallback;
  font-style: normal;
}

.cormorant_garamond_7b626b6c-module__p-a56W__variable {
  --font-cormorant: "Cormorant Garamond", "Cormorant Garamond Fallback";
}


/*# sourceMappingURL=%5Bnext%5D_internal_font_google_2679a3d9._.css.map*/