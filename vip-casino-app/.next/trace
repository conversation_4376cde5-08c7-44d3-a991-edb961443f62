[{"name": "hot-reloader", "duration": 31, "timestamp": 120273747158, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1748570874064, "traceId": "578109ad2f11406a"}, {"name": "setup-dev-bundler", "duration": 383928, "timestamp": 120273637323, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748570873954, "traceId": "578109ad2f11406a"}, {"name": "run-instrumentation-hook", "duration": 9, "timestamp": 120274037976, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748570874354, "traceId": "578109ad2f11406a"}, {"name": "start-dev-server", "duration": 597462, "timestamp": 120273442637, "id": 1, "tags": {"cpus": "10", "platform": "darwin", "memory.freeMem": "85753856", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8791261184", "memory.rss": "289062912", "memory.heapTotal": "134004736", "memory.heapUsed": "73199096"}, "startTime": 1748570873759, "traceId": "578109ad2f11406a"}, {"name": "compile-path", "duration": 1544283, "timestamp": 120312435750, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748570912752, "traceId": "578109ad2f11406a"}, {"name": "ensure-page", "duration": 1544713, "timestamp": 120312435518, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748570912752, "traceId": "578109ad2f11406a"}]