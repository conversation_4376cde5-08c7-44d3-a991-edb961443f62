const e=(e,t,n)=>n>t?t:n<e?e:n;const t={};function n(e){return"object"==typeof e&&null!==e}function r(e){let t;return()=>(void 0===t&&(t=e()),t)}const s=e=>e,o=(e,t)=>n=>t(e(n)),i=(...e)=>e.reduce(o),a=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r};const c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],u={value:null,addProjectionMetrics:null};function l(e,n){let r=!1,s=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>r=!0,a=c.reduce(((e,t)=>(e[t]=function(e,t){let n=new Set,r=new Set,s=!1,o=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},c=0;function l(t){i.has(t)&&(f.schedule(t),e()),c++,t(a)}const f={schedule:(e,t=!1,o=!1)=>{const a=o&&s?n:r;return t&&i.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),i.delete(e)},process:e=>{a=e,s?o=!0:(s=!0,[n,r]=[r,n],n.forEach(l),t&&u.value&&u.value.frameloop[t].push(c),c=0,n.clear(),s=!1,o&&(o=!1,f.process(e)))}};return f}(i,n?t:void 0),e)),{}),{setup:l,read:f,resolveKeyframes:d,preUpdate:g,update:h,preRender:p,render:m,postRender:v}=a,y=()=>{const i=t.useManualTiming?o.timestamp:performance.now();r=!1,t.useManualTiming||(o.delta=s?1e3/60:Math.max(Math.min(i-o.timestamp,40),1)),o.timestamp=i,o.isProcessing=!0,l.process(o),f.process(o),d.process(o),g.process(o),h.process(o),p.process(o),m.process(o),v.process(o),o.isProcessing=!1,r&&n&&(s=!1,e(y))};return{schedule:c.reduce(((t,n)=>{const i=a[n];return t[n]=(t,n=!1,a=!1)=>(r||(r=!0,s=!0,o.isProcessing||e(y)),i.schedule(t,n,a)),t}),{}),cancel:e=>{for(let t=0;t<c.length;t++)a[c[t]].cancel(e)},state:o,steps:a}}const{schedule:f,cancel:d,state:g,steps:h}=l("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s,!0),p=(e=>t=>"string"==typeof t&&t.startsWith(e))("var(--"),m=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,v={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},y={...v,transform:t=>e(0,1,t)},b=e=>Math.round(1e5*e)/1e5,w=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,E=(e,t)=>n=>Boolean("string"==typeof n&&x.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t)),W=(e,t,n)=>r=>{if("string"!=typeof r)return r;const[s,o,i,a]=r.match(w);return{[e]:parseFloat(s),[t]:parseFloat(o),[n]:parseFloat(i),alpha:void 0!==a?parseFloat(a):1}},L={...v,transform:t=>Math.round((t=>e(0,255,t))(t))},M={test:E("rgb","red"),parse:W("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+L.transform(e)+", "+L.transform(t)+", "+L.transform(n)+", "+b(y.transform(r))+")"};const S={test:E("#"),parse:function(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:M.transform},z=(e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}))("%"),A={test:E("hsl","hue"),parse:W("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+z.transform(b(t))+", "+z.transform(b(n))+", "+b(y.transform(r))+")"},B=e=>M.test(e)||S.test(e)||A.test(e),O=e=>M.test(e)?M.parse(e):A.test(e)?A.parse(e):S.parse(e),T=e=>"string"==typeof e?e:e.hasOwnProperty("red")?M.transform(e):A.transform(e),H=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const F="number",P="color",R=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function $(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let o=0;const i=t.replace(R,(e=>(B(e)?(r.color.push(o),s.push(P),n.push(O(e))):e.startsWith("var(")?(r.var.push(o),s.push("var"),n.push(e)):(r.number.push(o),s.push(F),n.push(parseFloat(e))),++o,"${}"))).split("${}");return{values:n,split:i,indexes:r,types:s}}function N(e){return $(e).values}function k(e){const{split:t,types:n}=$(e),r=t.length;return e=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],void 0!==e[o]){const t=n[o];s+=t===F?b(e[o]):t===P?T(e[o]):e[o]}return s}}const j=e=>"number"==typeof e?0:e;const q={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(w)?.length||0)+(e.match(H)?.length||0)>0},parse:N,createTransformer:k,getAnimatableNone:function(e){const t=N(e);return k(e)(t.map(j))}};function I(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function U(e,t){return n=>n>0?t:e}const G=(e,t,n)=>e+(t-e)*n,C=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},K=[S,M,A];function V(e){const t=(n=e,K.find((e=>e.test(n))));var n;if(!Boolean(t))return!1;let r=t.parse(e);return t===A&&(r=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let s=0,o=0,i=0;if(t/=100){const r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;s=I(a,r,e+1/3),o=I(a,r,e),i=I(a,r,e-1/3)}else s=o=i=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*i),alpha:r}}(r)),r}const D=(e,t)=>{const n=V(e),r=V(t);if(!n||!r)return U(e,t);const s={...n};return e=>(s.red=C(n.red,r.red,e),s.green=C(n.green,r.green,e),s.blue=C(n.blue,r.blue,e),s.alpha=G(n.alpha,r.alpha,e),M.transform(s))},J=new Set(["none","hidden"]);function Q(e,t){return n=>G(e,t,n)}function X(e){return"number"==typeof e?Q:"string"==typeof e?p(t=e)&&m.test(t.split("/*")[0].trim())?U:B(e)?D:_:Array.isArray(e)?Y:"object"==typeof e?B(e)?D:Z:U;var t}function Y(e,t){const n=[...e],r=n.length,s=e.map(((e,n)=>X(e)(e,t[n])));return e=>{for(let t=0;t<r;t++)n[t]=s[t](e);return n}}function Z(e,t){const n={...e,...t},r={};for(const s in n)void 0!==e[s]&&void 0!==t[s]&&(r[s]=X(e[s])(e[s],t[s]));return e=>{for(const t in r)n[t]=r[t](e);return n}}const _=(e,t)=>{const n=q.createTransformer(t),r=$(e),s=$(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?J.has(e)&&!s.values.length||J.has(t)&&!r.values.length?function(e,t){return J.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):i(Y(function(e,t){const n=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],i=e.indexes[o][r[o]],a=e.values[i]??0;n[s]=a,r[o]++}return n}(r,s),s.values),n):U(e,t)};function ee(e,t,n){if("number"==typeof e&&"number"==typeof t&&"number"==typeof n)return G(e,t,n);return X(e)(e,t)}function te(n,r,{clamp:o=!0,ease:c,mixer:u}={}){const l=n.length;if(r.length,1===l)return()=>r[0];if(2===l&&r[0]===r[1])return()=>r[1];const f=n[0]===n[1];n[0]>n[l-1]&&(n=[...n].reverse(),r=[...r].reverse());const d=function(e,n,r){const o=[],a=r||t.mix||ee,c=e.length-1;for(let t=0;t<c;t++){let r=a(e[t],e[t+1]);if(n){const e=Array.isArray(n)?n[t]||s:n;r=i(e,r)}o.push(r)}return o}(r,c,u),g=d.length,h=e=>{if(f&&e<n[0])return r[0];let t=0;if(g>1)for(;t<n.length-2&&!(e<n[t+1]);t++);const s=a(n[t],n[t+1],e);return d[t](s)};return o?t=>h(e(n[0],n[l-1],t)):h}function ne(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=a(0,t,r);e.push(G(n,1,s))}}(t,e.length-1),t}const re=r((()=>void 0!==window.ScrollTimeline));function se(e){return n(e)&&"offsetHeight"in e}function oe(e,t){let n;const r=()=>{const{currentTime:r}=t,s=(null===r?0:r.value)/100;n!==s&&e(s),n=s};return f.preUpdate(r,!0),()=>d(r)}const ie=new WeakMap;let ae;function ce(e,t){if(t){const{inlineSize:e,blockSize:n}=t[0];return{width:e,height:n}}return n(r=e)&&"ownerSVGElement"in r&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight};var r}function ue({target:e,contentRect:t,borderBoxSize:n}){ie.get(e)?.forEach((r=>{r({target:e,contentSize:t,get size(){return ce(e,n)}})}))}function le(e){e.forEach(ue)}function fe(e,t){ae||"undefined"!=typeof ResizeObserver&&(ae=new ResizeObserver(le));const n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let r=document;t&&(r=t.current);const s=n?.[e]??r.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}(e);return n.forEach((e=>{let n=ie.get(e);n||(n=new Set,ie.set(e,n)),n.add(t),ae?.observe(e)})),()=>{n.forEach((e=>{const n=ie.get(e);n?.delete(t),n?.size||ae?.unobserve(e)}))}}const de=new Set;let ge;function he(e){return de.add(e),ge||(ge=()=>{const e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};de.forEach((e=>e(t)))},window.addEventListener("resize",ge)),()=>{de.delete(e),!de.size&&ge&&(ge=void 0)}}const pe={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function me(e,t,n,r){const s=n[t],{length:o,position:i}=pe[t],c=s.current,u=n.time;s.current=e[`scroll${i}`],s.scrollLength=e[`scroll${o}`]-e[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=a(0,s.scrollLength,s.current);const l=r-u;var f,d;s.velocity=l>50?0:(f=s.current-c,(d=l)?f*(1e3/d):0)}const ve={start:0,center:.5,end:1};function ye(e,t,n=0){let r=0;if(e in ve&&(e=ve[e]),"string"==typeof e){const t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}const be=[0,0];function we(e,t,n,r){let s=Array.isArray(e)?e:be,o=0,i=0;return"number"==typeof e?s=[e,e]:"string"==typeof e&&(s=(e=e.trim()).includes(" ")?e.split(" "):[e,ve[e]?e:"0"]),o=ye(s[0],n,r),i=ye(s[1],t),o-i}const xe={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Ee={x:0,y:0};function We(t,n,r){const{offset:s=xe.All}=r,{target:o=t,axis:i="y"}=r,a="y"===i?"height":"width",c=o!==t?function(e,t){const n={x:0,y:0};let r=e;for(;r&&r!==t;)if(se(r))n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){const e=r.getBoundingClientRect();r=r.parentElement;const t=r.getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else{if(!(r instanceof SVGGraphicsElement))break;{const{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let s=null,o=r.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=r.parentNode;r=s}}return n}(o,t):Ee,u=o===t?{width:t.scrollWidth,height:t.scrollHeight}:function(e){return"getBBox"in e&&"svg"!==e.tagName?e.getBBox():{width:e.clientWidth,height:e.clientHeight}}(o),l={width:t.clientWidth,height:t.clientHeight};n[i].offset.length=0;let f=!n[i].interpolate;const d=s.length;for(let e=0;e<d;e++){const t=we(s[e],l[a],u[a],c[i]);f||t===n[i].interpolatorOffsets[e]||(f=!0),n[i].offset[e]=t}f&&(n[i].interpolate=te(n[i].offset,ne(s),{clamp:!1}),n[i].interpolatorOffsets=[...n[i].offset]),n[i].progress=e(0,1,n[i].interpolate(n[i].current))}function Le(e,t,n,r={}){return{measure:t=>{!function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight}(e,r.target,n),function(e,t,n){me(e,"x",t,n),me(e,"y",t,n),t.time=n}(e,n,t),(r.offset||r.target)&&We(e,n,r)},notify:()=>t(n)}}const Me=new WeakMap,Se=new WeakMap,ze=new WeakMap,Ae=e=>e===document.scrollingElement?window:e;function Be(e,{container:t=document.scrollingElement,...n}={}){if(!t)return s;let r=ze.get(t);r||(r=new Set,ze.set(t,r));const o=Le(t,e,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(r.add(o),!Me.has(t)){const e=()=>{for(const e of r)e.measure(g.timestamp);f.preUpdate(n)},n=()=>{for(const e of r)e.notify()},s=()=>f.read(e);Me.set(t,s);const o=Ae(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&Se.set(t,(a=s,"function"==typeof(i=t)?he(i):fe(i,a))),o.addEventListener("scroll",s,{passive:!0}),s()}var i,a;const c=Me.get(t);return f.read(c,!1,!0),()=>{d(c);const e=ze.get(t);if(!e)return;if(e.delete(o),e.size)return;const n=Me.get(t);Me.delete(t),n&&(Ae(t).removeEventListener("scroll",n),Se.get(t)?.(),window.removeEventListener("resize",n))}}const Oe=new Map;function Te({source:e,container:t,...n}){const{axis:r}=n;e&&(t=e);const s=Oe.get(t)??new Map;Oe.set(t,s);const o=n.target??"self",i=s.get(o)??{},a=r+(n.offset??[]).join(",");return i[a]||(i[a]=!n.target&&re()?new ScrollTimeline({source:t,axis:r}):function(e){const t={value:0},n=Be((n=>{t.value=100*n[e.axis].progress}),e);return{currentTime:t,cancel:n}}({container:t,...n})),i[a]}function He(e,{axis:t="y",container:n=document.scrollingElement,...r}={}){if(!n)return s;const o={axis:t,container:n,...r};return"function"==typeof e?function(e,t){return function(e){return 2===e.length}(e)?Be((n=>{e(n[t.axis].progress,n)}),t):oe(e,Te(t))}(e,o):function(e,t){const n=Te(t);return e.attachTimeline({timeline:t.target?void 0:n,observe:e=>(e.pause(),oe((t=>{e.time=e.duration*t}),n))})}(e,o)}export{He as scroll};
