# 仕様書 - 富裕層向けジャンケットアプリ デモ画面 UI/UX

## 1. はじめに

### 1.1. 本書の目的
本書は、「富裕層向けジャンケットアプリ デモ画面」のUI（ユーザーインターフェース）およびUX（ユーザーエクスペリエンス）に関する詳細な仕様を定義する。要件定義書に基づき、各画面のデザイン、レイアウト、主要コンポーネント、インタラクションについて記述する。

### 1.2. 対象画面
要件定義書の「3.2. デモ画面で実現すること (スコープ)」に記載された以下の画面を対象とする。
1.  スプラッシュ画面
2.  ホーム画面
3.  カジノ紹介一覧画面
4.  カジノ紹介詳細画面
5.  AIチャットボット画面
6.  ジャンケットへの問い合わせフォーム画面

## 2. 共通仕様

### 2.1. デザインコンセプト
*   **テーマ:** 「Exclusive Journey (エクスクルーシブ・ジャーニー)」 - 特別で洗練された旅への入り口
*   **トーン＆マナー:**
    *   高級感 (Luxury)
    *   洗練 (Sophisticated)
    *   信頼感 (Trustworthy)
    *   特別感 (Exclusive)
    *   ミニマル (Minimal) - 情報過多を避け、要素を絞り込む
*   **配色:**
    *   ベースカラー: ダークグレー (#333333) または オフホワイト (#F8F8F8)
    *   メインカラー: ゴールド (#B08D57) または 深みのあるロイヤルブルー (#003366)
    *   アクセントカラー: メインカラーの明度・彩度を調整したもの、または補色に近い色を限定的に使用。
*   **タイポグラフィ:**
    *   日本語フォント: 可読性の高い明朝体 (例: Noto Serif JP) または ゴシック体 (例: Noto Sans JP) のウェイトを使い分ける。見出しはやや装飾的なセリフ体も検討。
    *   英語フォント: エレガントなセリフ体 (例: Playfair Display, Cormorant Garamond) とモダンなサンセリフ体 (例: Montserrat, Lato) を組み合わせる。
    *   フォントサイズ、行間、字間は可読性と美観を考慮して調整。

### 2.2. 基本レイアウト
*   **ヘッダー:** 画面タイトル、戻るボタン（必要な場合）などを配置。シンプルに。
*   **フッターナビゲーション (タブバー):** 主要機能へのアクセスポイント (例: ホーム, カジノ一覧, チャット, マイページ(デモでは表示のみ))。アイコンとテキストラベル。
*   **コンテンツエリア:** 各画面の主要情報を表示。十分な余白を取り、見やすさを重視。

### 2.3. 主要UIコンポーネント
*   **ボタン:**
    *   プライマリボタン: メインカラーを基調とし、重要なアクションに使用。
    *   セカンダリボタン: アウトライン形式や薄い色で、補助的なアクションに使用。
    *   タップ時のフィードバック（視覚的変化）を考慮。
*   **カード:** 情報のまとまりを視覚的に分かりやすく表示。角丸、ドロップシャドウなどで質感を表現。
*   **画像:** 高解像度で魅力的なものを使用。アスペクト比を統一し、レイアウト崩れを防ぐ。
*   **アイコン:** シンプルで直感的に意味が伝わるデザイン。統一感を重視。
*   **入力フォーム:** ラベル、プレースホルダーを明確に。フォーカス時やエラー時のスタイルを定義。

### 2.4. ナビゲーション
*   **タブバー:** 主要画面間の遷移。
*   **画面内遷移:** リスト項目タップで詳細画面へ、など。
*   **戻るナビゲーション:** 左上に戻るボタン、またはスワイプジェスチャー（プラットフォーム標準に準拠）。
*   **トランジション:** 画面遷移時にはフェードイン/アウト、スライドなど、スムーズで上品なアニメーションを適度に用いる。

## 3. 画面詳細仕様

### 3.1. スプラッシュ画面 (Splash Screen)
*   **表示内容:**
    *   中央にアプリロゴ (例: VIP Access Concierge の頭文字をデザイン化したもの)
    *   ロゴの下にタグライン (例: "Your Gateway to Exclusive Casino Experiences")
    *   背景は単色またはグラデーション、あるいはアプリの世界観を表す微細なテクスチャ。
*   **インタラクション:**
    *   表示時間: 2-3秒程度。
    *   自動的にホーム画面へ遷移。

### 3.2. ホーム画面 (Home Screen)
*   **レイアウト:** 縦スクロール。
*   **ヘッダー:** アプリ名またはロゴ、通知アイコン (ダミー)。
*   **コンテンツエリア:**
    *   **ヒーローセクション (最上部):**
        *   注目のカジノや限定オファーを訴求する高品質なイメージスライダー (自動再生または手動)。
        *   各スライドにキャッチコピーと詳細への導線ボタン。
    *   **おすすめカジノセクション:**
        *   見出し: 「Featured Destinations」など。
        *   横スクロール可能なカードリスト形式で3-5件のカジノを表示。
        *   各カード: カジノの外観写真、カジノ名、短い紹介文。タップでカジノ紹介詳細画面へ遷移。
    *   **新着情報/オファーセクション:**
        *   見出し: 「Latest Offers & Events」など。
        *   縦に並ぶリスト形式で2-3件の情報を表示。
        *   各項目: サムネイル、タイトル、概要。タップで詳細ページ (デモでは省略可、または汎用的な情報表示画面へ)。
    *   **AIチャットボットへの導線:**
        *   画面下部固定のフローティングアクションボタン (FAB) またはセクションとして配置。
        *   アイコンと「AIコンシェルジュに相談」のようなテキスト。タップでAIチャットボット画面へ。
*   **フッターナビゲーション:** 「ホーム」「カジノ」「チャット」「マイページ(ダミー)」

### 3.3. カジノ紹介一覧画面 (Casino List Screen)
*   **ヘッダー:** 画面タイトル「カジノ一覧 (Destinations)」、検索アイコン (ダミー)、フィルターアイコン (ダミー)。
*   **コンテンツエリア:**
    *   縦スクロール可能なリスト形式またはグリッド形式 (2列など) でカジノを表示。
    *   各カジノアイテム:
        *   魅力的なカジノのイメージ画像。
        *   カジノ名。
        *   国/都市名。
        *   短いキャッチコピー (例: 「アジア屈指のエンターテイメントリゾート」)。
        *   (オプション) VIPランクや特典のアイコン表示。
    *   タップでカジノ紹介詳細画面へ遷移。
*   **フッターナビゲーション:** 同上。

### 3.4. カジノ紹介詳細画面 (Casino Detail Screen)
*   **ヘッダー:** カジノ名、戻るボタン、お気に入りアイコン (ダミー)。
*   **コンテンツエリア:**
    *   **メインビジュアル:** 大型のヒーローイメージまたは画像カルーセル。
    *   **基本情報セクション:**
        *   カジノ名、国/都市。
        *   ジャンケットからの推薦コメント (数行程度)。
    *   **タブUI (推奨):**
        *   「概要」: カジノの歴史、特徴、雰囲気、ドレスコードなど。
        *   「施設」: VIPルーム、レストラン、スパ、ショップなどの写真と説明。
        *   「ゲーム」: 主なテーブルゲーム、スロットマシンの種類など。
        *   「アクセス」: 所在地情報 (地図のUIイメージのみ)。
    *   **CTA (Call to Action) ボタン:**
        *   画面下部固定またはコンテンツの最後に配置。
        *   「このカジノについて問い合わせる」 (タップで問い合わせフォーム画面へ)。
*   **フッターナビゲーション:** 同上。

### 3.5. AIチャットボット画面 (AI Chatbot Screen)
*   **ヘッダー:** 画面タイトル「AIコンシェルジュ」、戻るボタン。
*   **コンテンツエリア:**
    *   メッセージがやり取りされるチャットインターフェース。
    *   **メッセージ表示:**
        *   ユーザーの質問: 右寄せの吹き出し。
        *   AIの応答: 左寄せの吹き出し。
        *   初回アクセス時にAI側からウェルカムメッセージと利用例を表示。
        *   (デモ用) いくつかの定型的な質問と応答のやり取りを事前に表示しておく。
    *   **入力エリア (画面下部):**
        *   テキスト入力フィールド (プレースホルダー: 「ご質問を入力してください」)。
        *   送信ボタン (アイコン)。
        *   (オプション) よくある質問の選択ボタン (例: 「ドレスコードは？」「予算は？」)。
*   **インタラクション:**
    *   ユーザーがテキストを入力して送信ボタンをタップすると、そのメッセージがチャット履歴に追加される (実際の送信処理はなし)。
    *   (デモ用) 送信後、少し遅れてダミーのAI応答メッセージが表示される。
*   **フッターナビゲーション:** 同上。

### 3.6. ジャンケットへの問い合わせフォーム画面 (Inquiry Form Screen)
*   **ヘッダー:** 画面タイトル「お問い合わせ」、戻るボタン。
*   **コンテンツエリア:**
    *   フォーム導入文: 「担当ジャンケットが個別にご対応いたします。お気軽にお問い合わせください。」
    *   **入力項目:**
        *   お名前 (必須)
        *   ご連絡先 (メールアドレスまたは電話番号、選択式または両方、必須)
        *   ご興味のあるカジノ (ドロップダウンまたは自由記述、任意)
        *   ご希望の時期 (任意)
        *   お問い合わせ内容 (テキストエリア、必須)
    *   プライバシーポリシーへの同意チェックボックス (ダミー)。
    *   **送信ボタン:** 「送信する」。タップで「送信完了しました」という趣旨のメッセージを表示するポップアップまたは別画面に遷移（デモ用）。
*   **フッターナビゲーション:** 同上。

## 4. その他
*   **ローディング表現:** 画面遷移時やデータ取得を模倣する箇所では、シンプルなローディングスピナーやスケルトンスクリーンを表示することを検討。
*   **エラー表示:** デモでは積極的なエラー表示は不要だが、フォーム入力で必須項目が未入力の場合の表現などを軽くイメージしておく。

---