# 要件定義書 - 富裕層向けジャンケットアプリ デモ画面

## 1. はじめに

### 1.1. プロジェクトの目的
本プロジェクトの目的は、日本の富裕層をターゲットとしたカジノジャンケット向け集客アプリの「おしゃれなスマホ仕様のデモ画面」を作成することである。このデモ画面は、アプリの主要な機能とユーザー体験のイメージを視覚的に伝え、関係者間での認識合わせおよび今後の開発に向けたフィードバック収集を目的とする。

### 1.2. プロジェクトの背景
日本の富裕層をカジノへ誘致するジャンケット業務において、効率的な集客と顧客エンゲージメント向上が課題となっている。本アプリは、魅力的な情報提供とAIチャットボットによる簡易相談を通じて、富裕層ユーザーの関心を喚起し、ジャンケットへの問い合わせを促進することを目的とする。本デモは、そのコンセプトを具現化する第一歩である。

### 1.3. 対象読者
*   プロジェクト関係者 (企画者、デザイナー、開発者など)
*   投資家、意思決定者 (該当する場合)

### 1.4. 用語定義
*   **ジャンケット:** カジノに富裕層顧客を誘致し、滞在中の世話をする事業者。
*   **富裕層:** 高所得者層、高額な消費が可能な層。
*   **デモ画面:** アプリの主要な画面デザインと基本的な画面遷移を視覚化したもの。実際のデータ連携や全ての機能の実装は行わない。
*   **AIチャットボット:** ユーザーからの質問に自動で応答するシステム。本デモではUIのイメージのみ。

## 2. アプリケーション概要 (デモで表現する範囲)

### 2.1. アプリケーション名 (仮)
「VIP Access Concierge (仮)」

### 2.2. コンセプト
「選ばれたあなたへ、至高のカジノ体験への扉を開く。パーソナルAIコンシェルジュがサポート。」

### 2.3. ターゲットユーザー
*   日本の富裕層
*   海外カジノ旅行に興味がある、または既に経験がある層
*   パーソナルなサービス、特別感を重視する層

### 2.4. 提供価値 (デモで示唆する価値)
*   厳選されたカジノ情報の閲覧
*   ジャンケット経由での限定的なオファーや体験の紹介
*   AIチャットボットによる気軽な質問・相談（UIイメージ）
*   ジャンケットへのスムーズな問い合わせ導線

## 3. デモ画面の範囲と目的

### 3.1. デモ画面の目的
*   アプリの「おしゃれさ」「高級感」「特別感」を視覚的に表現する。
*   主要なユーザーフローを体験できるようにする。
*   搭載予定の主要機能のUIイメージを具体化する。

### 3.2. デモ画面で実現すること (スコープ)
*   主要な数画面のデザインと静的な画面遷移。
*   「おしゃれ」と感じられるUI/UXの提案。
    *   洗練されたタイポグラフィ、高品質な画像の使用
    *   シンプルかつ直感的なナビゲーション
    *   高級感を意識した配色
*   以下の主要画面のデザインカンプ（またはインタラクティブプロトタイプ）:
    1.  スプラッシュ画面
    2.  ホーム画面 (おすすめカジノ、新着オファーなど)
    3.  カジノ紹介一覧画面
    4.  カジノ紹介詳細画面
    5.  AIチャットボット画面 (入力UIと応答表示のイメージ)
    6.  ジャンケットへの問い合わせフォーム画面 (入力項目がわかる程度)

### 3.3. デモ画面で実現しないこと (スコープ外)
*   実際のデータ連携、API連携
*   ユーザー登録、ログイン機能
*   AIチャットボットの実際の応答ロジック
*   プッシュ通知機能
*   ジャンケット向け管理機能
*   決済機能
*   多言語対応の全実装（デザイン上、英語表記を一部入れる程度は可）

## 4. 機能要件 (デモ画面で表現する要素)

### 4.1. ユーザー向け機能（UI/UX表現）
| No. | 機能名                 | 概要                                                                 | デモでの表現                                                                 |
|-----|------------------------|----------------------------------------------------------------------|------------------------------------------------------------------------------|
| 4.1.1 | スプラッシュ画面         | アプリ起動時に表示される画面。ブランドロゴやタグラインを表示。                     | 洗練されたデザインのロゴとタグライン、短時間表示後の自動遷移。                                 |
| 4.1.2 | ホーム画面             | おすすめカジノ、新着情報、特典への導線などを集約して表示。                        | 高品質なイメージバナー、カード型UIによる情報提示、スムーズなスクロール。                            |
| 4.1.3 | カジノ紹介一覧画面     | 提携カジノのリストを表示。フィルタリングやソートのUIイメージ（機能はダミー）。       | 各カジノのサムネイル画像、名称、キャッチコピーをリスト形式またはグリッド形式で表示。                       |
| 4.1.4 | カジノ紹介詳細画面     | 個別カジノの詳細情報（写真、施設概要、ゲーム、ジャンケットからのコメント）を表示。 | 複数の高品質画像ギャラリー、タブ切り替えによる情報整理、予約/問い合わせへのCTAボタン。                   |
| 4.1.5 | AIチャットボット画面     | ユーザーが質問を入力し、AIからの応答を受け取るインターフェース。                     | メッセージアプリ風のUI、質問入力フィールド、応答表示エリアのビジュアル。実際の応答ロジックは不要。           |
| 4.1.6 | ジャンケット問い合わせ画面 | ユーザーがジャンケットに直接問い合わせるためのフォーム。                           | シンプルで入力しやすいフォーム項目（氏名、連絡先、問い合わせ内容など）と送信ボタンのUI。                 |

### 4.2. 非機能要件 (デモ画面で重視する点)
*   **デザイン:**
    *   全体的に高級感があり、洗練されていること。
    *   ダークモード基調または白基調でアクセントカラーを効果的に使用するなど、ターゲット層に響くデザイン。
    *   使用する画像は高品質で、カジノの華やかさや特別感を伝えるもの。
    *   フォントは可読性が高く、かつエレガントなものを選定。
*   **操作性:**
    *   直感的で迷わないナビゲーション。
    *   スムーズな画面遷移、アニメーション（過度でない範囲で）。
    *   タップ領域の適切な確保。
*   **パフォーマンス:**
    *   デモ画面であっても、軽快な動作感を意識する（特に画面遷移時）。

## 5. 成果物
*   主要画面のデザインカンプ (Figma, Adobe XDなどの形式、または画像ファイル)
*   インタラクティブプロトタイプ (Figma, Protopieなどで作成、操作可能なもの)
*   本要件定義書

---